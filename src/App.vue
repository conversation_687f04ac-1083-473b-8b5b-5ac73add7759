<template>
  <div id="app" class="app-container">
    <!-- 顶部导航栏 -->
    <AppHeader />
    
    <!-- 主内容区 -->
    <main class="main-content">
      <router-view v-slot="{ Component }">
        <transition name="fade" mode="out-in">
          <component :is="Component" />
        </transition>
      </router-view>
    </main>
    
    <!-- 全局加载组件 -->
    <GlobalLoading v-if="isLoading" />
    
    <!-- 全局通知组件 -->
    <GlobalNotification />
  </div>
</template>

<script setup>
import { ref } from 'vue'
import AppHeader from '@/components/layout/AppHeader.vue'
import GlobalLoading from '@/components/common/GlobalLoading.vue'
import GlobalNotification from '@/components/common/GlobalNotification.vue'

// 全局加载状态
const isLoading = ref(false)
</script>

<style lang="scss">
.app-container {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  background: var(--gray-50);
}

.main-content {
  flex: 1;
  padding: var(--spacing-lg);
  overflow-x: hidden;
}

// 路由过渡动画
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}
</style>
