<template>
  <div class="dashboard">
    <!-- 欢迎区域 -->
    <div class="welcome-section">
      <div class="welcome-content">
        <h1 class="welcome-title">
          欢迎回来，{{ userStore.user.name }}！
        </h1>
        <p class="welcome-subtitle">
          今天是个备课的好日子，让我们开始创造精彩的历史课堂吧
        </p>
      </div>
      <div class="welcome-stats">
        <div class="stat-item">
          <div class="stat-value">{{ userStore.stats.designs }}</div>
          <div class="stat-label">教学设计</div>
        </div>
        <div class="stat-item">
          <div class="stat-value">{{ userStore.stats.ppts }}</div>
          <div class="stat-label">PPT课件</div>
        </div>
        <div class="stat-item">
          <div class="stat-value">{{ userStore.stats.quizzes }}</div>
          <div class="stat-label">试题试卷</div>
        </div>
        <div class="stat-item">
          <div class="stat-value">{{ userStore.stats.timeSaved }}</div>
          <div class="stat-label">节省时间(小时)</div>
        </div>
      </div>
    </div>

    <!-- 快速操作 -->
    <div class="quick-actions">
      <h2 class="section-title">快速开始</h2>
      <div class="action-grid">
        <router-link to="/teaching-design" class="action-card">
          <div class="action-icon">✨</div>
          <h3 class="action-title">创建教学设计</h3>
          <p class="action-desc">使用AI快速生成结构化的教学设计</p>
        </router-link>
        
        <router-link to="/ppt-generator" class="action-card">
          <div class="action-icon">🎯</div>
          <h3 class="action-title">生成PPT课件</h3>
          <p class="action-desc">将教学设计转换为精美的PPT演示</p>
        </router-link>
        
        <router-link to="/quiz-generator" class="action-card">
          <div class="action-icon">📊</div>
          <h3 class="action-title">智能组卷</h3>
          <p class="action-desc">根据知识点自动生成测试题目</p>
        </router-link>
        
        <router-link to="/resources" class="action-card">
          <div class="action-icon">📚</div>
          <h3 class="action-title">浏览资源库</h3>
          <p class="action-desc">查看和管理您的教学资源</p>
        </router-link>
      </div>
    </div>

    <!-- 最近使用 -->
    <div class="recent-section">
      <h2 class="section-title">最近使用</h2>
      <div class="recent-grid">
        <div v-for="item in recentItems" :key="item.id" class="recent-item">
          <div class="recent-icon">{{ item.icon }}</div>
          <div class="recent-content">
            <h4 class="recent-title">{{ item.title }}</h4>
            <p class="recent-desc">{{ item.description }}</p>
            <span class="recent-time">{{ item.time }}</span>
          </div>
          <div class="recent-actions">
            <button class="btn btn-secondary btn-sm">编辑</button>
            <button class="btn btn-primary btn-sm">使用</button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useUserStore } from '@/stores/user'

const userStore = useUserStore()

// 最近使用的资源
const recentItems = ref([
  {
    id: 1,
    icon: '📝',
    title: '明朝的建立',
    description: '七年级上册第三单元新授课教学设计',
    time: '2小时前',
    type: 'design'
  },
  {
    id: 2,
    icon: '🎯',
    title: '秦统一六国',
    description: '历史发展脉络PPT课件',
    time: '1天前',
    type: 'ppt'
  },
  {
    id: 3,
    icon: '📊',
    title: '汉朝政治制度',
    description: '单元测试卷（25题）',
    time: '3天前',
    type: 'quiz'
  }
])

onMounted(() => {
  // 初始化用户数据
  userStore.initializeUser()
})
</script>

<style lang="scss" scoped>
.dashboard {
  max-width: 1200px;
  margin: 0 auto;
}

.welcome-section {
  background: linear-gradient(135deg, var(--primary), var(--accent));
  border-radius: var(--radius-xl);
  padding: var(--spacing-xl);
  color: white;
  margin-bottom: var(--spacing-xl);
  display: flex;
  justify-content: space-between;
  align-items: center;
  
  @include mobile {
    flex-direction: column;
    text-align: center;
    gap: var(--spacing-lg);
  }
}

.welcome-title {
  font-size: 2.5rem;
  font-weight: 800;
  margin-bottom: var(--spacing-sm);
  
  @include mobile {
    font-size: 2rem;
  }
}

.welcome-subtitle {
  font-size: 1.125rem;
  opacity: 0.9;
}

.welcome-stats {
  display: flex;
  gap: var(--spacing-xl);
  
  @include mobile {
    gap: var(--spacing-lg);
  }
}

.stat-item {
  text-align: center;
}

.stat-value {
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: var(--spacing-xs);
}

.stat-label {
  font-size: 0.875rem;
  opacity: 0.8;
}

.section-title {
  font-size: 1.5rem;
  font-weight: 700;
  margin-bottom: var(--spacing-lg);
  color: var(--gray-900);
}

.quick-actions {
  margin-bottom: var(--spacing-xl);
}

.action-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: var(--spacing-lg);
}

.action-card {
  background: white;
  border-radius: var(--radius-lg);
  padding: var(--spacing-xl);
  text-decoration: none;
  color: inherit;
  border: 1px solid var(--gray-200);
  transition: all 0.3s ease;
  
  &:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-lg);
    border-color: var(--primary);
  }
}

.action-icon {
  font-size: 3rem;
  margin-bottom: var(--spacing-md);
}

.action-title {
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: var(--spacing-sm);
  color: var(--gray-900);
}

.action-desc {
  color: var(--gray-600);
  line-height: 1.5;
}

.recent-section {
  margin-bottom: var(--spacing-xl);
}

.recent-grid {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}

.recent-item {
  background: white;
  border-radius: var(--radius-lg);
  padding: var(--spacing-lg);
  border: 1px solid var(--gray-200);
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  transition: all 0.3s ease;
  
  &:hover {
    box-shadow: var(--shadow-md);
    border-color: var(--gray-300);
  }
}

.recent-icon {
  font-size: 2rem;
  width: 60px;
  height: 60px;
  background: var(--gray-100);
  border-radius: var(--radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
}

.recent-content {
  flex: 1;
}

.recent-title {
  font-size: 1.125rem;
  font-weight: 600;
  margin-bottom: var(--spacing-xs);
  color: var(--gray-900);
}

.recent-desc {
  color: var(--gray-600);
  margin-bottom: var(--spacing-xs);
}

.recent-time {
  font-size: 0.875rem;
  color: var(--gray-500);
}

.recent-actions {
  display: flex;
  gap: var(--spacing-sm);
  
  @include mobile {
    flex-direction: column;
  }
}
</style>
