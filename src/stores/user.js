import { defineStore } from 'pinia'
import { ref, computed } from 'vue'

export const useUserStore = defineStore('user', () => {
  // 状态
  const user = ref({
    id: '1',
    name: '张老师',
    email: '<EMAIL>',
    school: '北京市第一中学',
    subject: '历史',
    grade: '七年级',
    phone: '138****8888',
    avatar: '',
    role: 'teacher'
  })

  const isLoggedIn = ref(true)
  const preferences = ref({
    defaultTemplate: 'intro',
    defaultDuration: '45',
    questionTypes: '综合题型',
    pptStyle: '现代简约风',
    theme: 'light',
    notifications: {
      email: true,
      browser: true,
      updates: true
    }
  })

  const stats = ref({
    designs: 12,
    ppts: 8,
    quizzes: 15,
    timeSaved: 48,
    totalResources: 35,
    studentReach: 280
  })

  // 计算属性
  const userInitials = computed(() => {
    return user.value.name.charAt(0)
  })

  const isTeacher = computed(() => {
    return user.value.role === 'teacher'
  })

  // 方法
  const login = async (credentials) => {
    try {
      // 模拟登录API调用
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      // 更新用户信息
      user.value = {
        ...user.value,
        ...credentials
      }
      isLoggedIn.value = true
      
      // 保存到localStorage
      localStorage.setItem('user', JSON.stringify(user.value))
      localStorage.setItem('isLoggedIn', 'true')
      
      return { success: true }
    } catch (error) {
      return { success: false, error: error.message }
    }
  }

  const logout = () => {
    user.value = {}
    isLoggedIn.value = false
    
    // 清除localStorage
    localStorage.removeItem('user')
    localStorage.removeItem('isLoggedIn')
  }

  const updateProfile = async (profileData) => {
    try {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 500))
      
      user.value = {
        ...user.value,
        ...profileData
      }
      
      // 保存到localStorage
      localStorage.setItem('user', JSON.stringify(user.value))
      
      return { success: true }
    } catch (error) {
      return { success: false, error: error.message }
    }
  }

  const updatePreferences = async (newPreferences) => {
    try {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 300))
      
      preferences.value = {
        ...preferences.value,
        ...newPreferences
      }
      
      // 保存到localStorage
      localStorage.setItem('preferences', JSON.stringify(preferences.value))
      
      return { success: true }
    } catch (error) {
      return { success: false, error: error.message }
    }
  }

  const updateStats = (newStats) => {
    stats.value = {
      ...stats.value,
      ...newStats
    }
  }

  // 初始化用户数据
  const initializeUser = () => {
    const savedUser = localStorage.getItem('user')
    const savedIsLoggedIn = localStorage.getItem('isLoggedIn')
    const savedPreferences = localStorage.getItem('preferences')
    
    if (savedUser && savedIsLoggedIn === 'true') {
      user.value = JSON.parse(savedUser)
      isLoggedIn.value = true
    }
    
    if (savedPreferences) {
      preferences.value = {
        ...preferences.value,
        ...JSON.parse(savedPreferences)
      }
    }
  }

  return {
    // 状态
    user,
    isLoggedIn,
    preferences,
    stats,
    
    // 计算属性
    userInitials,
    isTeacher,
    
    // 方法
    login,
    logout,
    updateProfile,
    updatePreferences,
    updateStats,
    initializeUser
  }
})
