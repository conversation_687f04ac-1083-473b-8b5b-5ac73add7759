import { createRouter, createWebHistory } from 'vue-router'

// 路由组件懒加载
const Dashboard = () => import('@/views/Dashboard.vue')
const TeachingDesign = () => import('@/views/TeachingDesign.vue')
const PPTGenerator = () => import('@/views/PPTGenerator.vue')
const QuizGenerator = () => import('@/views/QuizGenerator.vue')
const ResourceLibrary = () => import('@/views/ResourceLibrary.vue')
const Profile = () => import('@/views/Profile.vue')

const routes = [
  {
    path: '/',
    redirect: '/dashboard'
  },
  {
    path: '/dashboard',
    name: 'Dashboard',
    component: Dashboard,
    meta: {
      title: '工作台',
      icon: '🏠'
    }
  },
  {
    path: '/teaching-design',
    name: 'TeachingDesign',
    component: TeachingDesign,
    meta: {
      title: '教学设计',
      icon: '📝'
    }
  },
  {
    path: '/ppt-generator',
    name: 'PPTGenerator',
    component: PPTGenerator,
    meta: {
      title: 'PPT生成',
      icon: '🎯'
    }
  },
  {
    path: '/quiz-generator',
    name: 'QuizGenerator',
    component: QuizGenerator,
    meta: {
      title: '智能组卷',
      icon: '📊'
    }
  },
  {
    path: '/resources',
    name: 'ResourceLibrary',
    component: ResourceLibrary,
    meta: {
      title: '资源库',
      icon: '📚'
    }
  },
  {
    path: '/profile',
    name: 'Profile',
    component: Profile,
    meta: {
      title: '个人中心',
      icon: '👤'
    }
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes,
  scrollBehavior(to, from, savedPosition) {
    if (savedPosition) {
      return savedPosition
    } else {
      return { top: 0 }
    }
  }
})

// 路由守卫
router.beforeEach((to, from, next) => {
  // 设置页面标题
  if (to.meta.title) {
    document.title = `${to.meta.title} - 智趣AI`
  }
  next()
})

export default router
