// 常用混入

// 响应式断点
@mixin mobile {
  @media (max-width: 767px) {
    @content;
  }
}

@mixin tablet {
  @media (min-width: 768px) and (max-width: 1023px) {
    @content;
  }
}

@mixin desktop {
  @media (min-width: 1024px) {
    @content;
  }
}

@mixin large-desktop {
  @media (min-width: 1280px) {
    @content;
  }
}

// 文本省略
@mixin text-ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

@mixin text-ellipsis-multiline($lines: 2) {
  display: -webkit-box;
  -webkit-line-clamp: $lines;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

// 居中
@mixin center-absolute {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

@mixin center-flex {
  display: flex;
  align-items: center;
  justify-content: center;
}

// 清除浮动
@mixin clearfix {
  &::after {
    content: '';
    display: table;
    clear: both;
  }
}

// 按钮样式混入
@mixin button-variant($bg-color, $text-color: white, $hover-bg: darken($bg-color, 10%)) {
  background-color: $bg-color;
  color: $text-color;
  border: none;
  
  &:hover:not(:disabled) {
    background-color: $hover-bg;
  }
  
  &:focus {
    box-shadow: 0 0 0 3px rgba($bg-color, 0.3);
  }
}

// 卡片阴影
@mixin card-shadow($level: 1) {
  @if $level == 1 {
    box-shadow: var(--shadow-sm);
  } @else if $level == 2 {
    box-shadow: var(--shadow-md);
  } @else if $level == 3 {
    box-shadow: var(--shadow-lg);
  } @else {
    box-shadow: var(--shadow-xl);
  }
}

// 过渡动画
@mixin transition($properties: all, $duration: 0.3s, $timing: ease) {
  transition: $properties $duration $timing;
}

// 渐变背景
@mixin gradient-bg($start-color, $end-color, $direction: 135deg) {
  background: linear-gradient($direction, $start-color, $end-color);
}

// 输入框焦点样式
@mixin input-focus($color: var(--primary)) {
  &:focus {
    border-color: $color;
    box-shadow: 0 0 0 3px rgba($color, 0.1);
    outline: none;
  }
}

// 滚动条样式
@mixin custom-scrollbar($width: 8px, $track-color: var(--gray-100), $thumb-color: var(--gray-300)) {
  &::-webkit-scrollbar {
    width: $width;
    height: $width;
  }
  
  &::-webkit-scrollbar-track {
    background: $track-color;
    border-radius: calc($width / 2);
  }
  
  &::-webkit-scrollbar-thumb {
    background: $thumb-color;
    border-radius: calc($width / 2);
    
    &:hover {
      background: darken($thumb-color, 10%);
    }
  }
}
