<template>
  <header class="app-header">
    <div class="header-content">
      <!-- Logo -->
      <router-link to="/dashboard" class="logo">
        <span class="logo-icon">🎓</span>
        <span class="logo-text">智趣AI</span>
      </router-link>
      
      <!-- 导航菜单 -->
      <nav class="nav-menu">
        <router-link
          v-for="route in navRoutes"
          :key="route.name"
          :to="route.path"
          class="nav-link"
          :class="{ active: $route.name === route.name }"
        >
          <span class="nav-icon">{{ route.meta.icon }}</span>
          <span class="nav-text">{{ route.meta.title }}</span>
        </router-link>
      </nav>
      
      <!-- 用户信息 -->
      <div class="user-info">
        <div class="user-avatar" @click="toggleUserMenu">
          {{ userStore.user.name.charAt(0) }}
        </div>
        <span class="user-name">{{ userStore.user.name }}</span>
        
        <!-- 用户菜单 -->
        <div v-if="showUserMenu" class="user-menu" @click.stop>
          <router-link to="/profile" class="menu-item" @click="showUserMenu = false">
            <span>👤</span>
            个人中心
          </router-link>
          <div class="menu-item" @click="handleLogout">
            <span>🚪</span>
            退出登录
          </div>
        </div>
      </div>
    </div>
  </header>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import { useUserStore } from '@/stores/user'

const router = useRouter()
const userStore = useUserStore()

// 用户菜单显示状态
const showUserMenu = ref(false)

// 导航路由
const navRoutes = computed(() => {
  return router.getRoutes().filter(route => 
    route.meta && route.meta.title && route.path !== '/'
  )
})

// 切换用户菜单
const toggleUserMenu = () => {
  showUserMenu.value = !showUserMenu.value
}

// 处理退出登录
const handleLogout = () => {
  userStore.logout()
  router.push('/login')
  showUserMenu.value = false
}

// 点击外部关闭菜单
const handleClickOutside = (event) => {
  if (!event.target.closest('.user-info')) {
    showUserMenu.value = false
  }
}

onMounted(() => {
  document.addEventListener('click', handleClickOutside)
})

onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside)
})
</script>

<style lang="scss" scoped>
.app-header {
  background: white;
  border-bottom: 1px solid var(--gray-200);
  box-shadow: var(--shadow-sm);
  position: sticky;
  top: 0;
  z-index: var(--z-sticky);
}

.header-content {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 var(--spacing-lg);
  height: 64px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.logo {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  text-decoration: none;
  color: var(--gray-900);
  font-weight: 700;
  font-size: 20px;
  
  .logo-icon {
    font-size: 24px;
  }
  
  &:hover {
    color: var(--primary);
  }
}

.nav-menu {
  display: flex;
  gap: var(--spacing-md);
  
  @include mobile {
    display: none;
  }
}

.nav-link {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  padding: var(--spacing-sm) var(--spacing-md);
  border-radius: var(--radius-md);
  text-decoration: none;
  color: var(--gray-600);
  font-weight: 500;
  transition: all 0.3s ease;
  
  .nav-icon {
    font-size: 18px;
  }
  
  &:hover {
    background: var(--gray-100);
    color: var(--gray-900);
  }
  
  &.active {
    background: var(--primary-bg);
    color: var(--primary);
  }
}

.user-info {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  position: relative;
  cursor: pointer;
}

.user-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: var(--primary);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 16px;
  transition: all 0.3s ease;
  
  &:hover {
    transform: scale(1.05);
    box-shadow: var(--shadow-md);
  }
}

.user-name {
  font-weight: 500;
  color: var(--gray-700);
  
  @include mobile {
    display: none;
  }
}

.user-menu {
  position: absolute;
  top: 100%;
  right: 0;
  margin-top: var(--spacing-sm);
  background: white;
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-lg);
  border: 1px solid var(--gray-200);
  min-width: 160px;
  overflow: hidden;
  z-index: var(--z-dropdown);
}

.menu-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-md);
  color: var(--gray-700);
  text-decoration: none;
  transition: background-color 0.3s ease;
  cursor: pointer;
  
  &:hover {
    background: var(--gray-50);
  }
  
  &:not(:last-child) {
    border-bottom: 1px solid var(--gray-100);
  }
}
</style>
