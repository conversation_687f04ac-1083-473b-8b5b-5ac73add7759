<template>
  <teleport to="body">
    <transition-group name="notification" tag="div" class="notification-container">
      <div
        v-for="notification in notifications"
        :key="notification.id"
        :class="['notification', `notification-${notification.type}`]"
        @click="removeNotification(notification.id)"
      >
        <div class="notification-icon">
          {{ getIcon(notification.type) }}
        </div>
        <div class="notification-content">
          <h4 v-if="notification.title" class="notification-title">
            {{ notification.title }}
          </h4>
          <p class="notification-message">
            {{ notification.message }}
          </p>
        </div>
        <button class="notification-close" @click.stop="removeNotification(notification.id)">
          ×
        </button>
      </div>
    </transition-group>
  </teleport>
</template>

<script setup>
import { ref } from 'vue'

const notifications = ref([])

const getIcon = (type) => {
  const icons = {
    success: '✅',
    error: '❌',
    warning: '⚠️',
    info: 'ℹ️'
  }
  return icons[type] || icons.info
}

const addNotification = (notification) => {
  const id = Date.now() + Math.random()
  const newNotification = {
    id,
    type: 'info',
    duration: 4000,
    ...notification
  }
  
  notifications.value.push(newNotification)
  
  if (newNotification.duration > 0) {
    setTimeout(() => {
      removeNotification(id)
    }, newNotification.duration)
  }
}

const removeNotification = (id) => {
  const index = notifications.value.findIndex(n => n.id === id)
  if (index > -1) {
    notifications.value.splice(index, 1)
  }
}

// 暴露方法给全局使用
defineExpose({
  addNotification,
  removeNotification
})
</script>

<style lang="scss" scoped>
.notification-container {
  position: fixed;
  top: var(--spacing-lg);
  right: var(--spacing-lg);
  z-index: var(--z-toast);
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
  max-width: 400px;
  
  @include mobile {
    left: var(--spacing-md);
    right: var(--spacing-md);
    max-width: none;
  }
}

.notification {
  background: white;
  border-radius: var(--radius-lg);
  padding: var(--spacing-md);
  box-shadow: var(--shadow-lg);
  border: 1px solid var(--gray-200);
  display: flex;
  align-items: flex-start;
  gap: var(--spacing-sm);
  cursor: pointer;
  transition: all 0.3s ease;
  
  &:hover {
    transform: translateX(-4px);
    box-shadow: var(--shadow-xl);
  }
  
  &.notification-success {
    border-left: 4px solid var(--success);
  }
  
  &.notification-error {
    border-left: 4px solid var(--error);
  }
  
  &.notification-warning {
    border-left: 4px solid var(--warning);
  }
  
  &.notification-info {
    border-left: 4px solid var(--info);
  }
}

.notification-icon {
  font-size: 1.25rem;
  margin-top: 2px;
}

.notification-content {
  flex: 1;
}

.notification-title {
  font-size: 1rem;
  font-weight: 600;
  margin-bottom: var(--spacing-xs);
  color: var(--gray-900);
}

.notification-message {
  font-size: 0.875rem;
  color: var(--gray-600);
  line-height: 1.4;
}

.notification-close {
  background: none;
  border: none;
  font-size: 1.25rem;
  color: var(--gray-400);
  cursor: pointer;
  padding: 0;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.3s ease;
  
  &:hover {
    background: var(--gray-100);
    color: var(--gray-600);
  }
}

// 过渡动画
.notification-enter-active,
.notification-leave-active {
  transition: all 0.3s ease;
}

.notification-enter-from {
  opacity: 0;
  transform: translateX(100%);
}

.notification-leave-to {
  opacity: 0;
  transform: translateX(100%);
}

.notification-move {
  transition: transform 0.3s ease;
}
</style>
