<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智趣AI - PPT智能转换模块</title>
    <style>
        :root {
            /* 色彩体系 */
            --primary-blue: #2B5CE6;
            --light-blue: #E8F0FE;
            --dark-blue: #1A237E;
            --success-green: #34A853;
            --warning-orange: #FF9800;
            --error-red: #EA4335;
            --ai-purple: #8E24AA;
            --neutral-gray: #5F6368;
            --interactive: #4285F4;
            --creative: #F4B400;
            --analysis: #0F9D58;
            --media: #DB4437;
            
            /* 中性色 */
            --gray-50: #F8F9FA;
            --gray-100: #F1F3F4;
            --gray-200: #E8EAED;
            --gray-300: #DADCE0;
            --gray-400: #BDC1C6;
            --gray-500: #9AA0A6;
            --gray-600: #80868B;
            --gray-700: #5F6368;
            --gray-800: #3C4043;
            --gray-900: #202124;
            
            /* 间距系统 */
            --spacing-xs: 4px;
            --spacing-sm: 8px;
            --spacing-md: 16px;
            --spacing-lg: 24px;
            --spacing-xl: 32px;
            --spacing-xxl: 48px;
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: "PingFang SC", "Helvetica Neue", Arial, sans-serif;
        }
        
        body {
            background-color: var(--gray-100);
            color: var(--gray-900);
            line-height: 1.5;
        }
        
        .app-container {
            min-width: 1440px;
            height: 100vh;
            display: flex;
            flex-direction: column;
        }
        
        /* 顶部导航栏 */
        .app-header {
            height: 48px;
            background-color: white;
            border-bottom: 1px solid var(--gray-200);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 var(--spacing-md);
        }
        
        .app-logo {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 18px;
            color: var(--dark-blue);
        }
        
        .app-logo span {
            margin-right: var(--spacing-xs);
        }
        
        .main-nav {
            display: flex;
            gap: var(--spacing-md);
        }
        
        .nav-item {
            padding: var(--spacing-xs) var(--spacing-sm);
            border-radius: 4px;
            cursor: pointer;
        }
        
        .nav-item:hover {
            background-color: var(--light-blue);
        }
        
        .user-profile {
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
        }
        
        .user-avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background-color: var(--primary-blue);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 500;
        }
        
        /* 子导航栏 */
        .sub-header {
            height: 40px;
            background-color: white;
            border-bottom: 1px solid var(--gray-200);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 var(--spacing-md);
            font-size: 14px;
        }
        
        .breadcrumb {
            display: flex;
            align-items: center;
            gap: var(--spacing-xs);
        }
        
        .breadcrumb-item {
            color: var(--gray-700);
        }
        
        .breadcrumb-separator {
            color: var(--gray-500);
        }
        
        .action-buttons {
            display: flex;
            gap: var(--spacing-sm);
        }
        
        .action-button {
            background: none;
            border: none;
            cursor: pointer;
            font-size: 16px;
            color: var(--gray-700);
        }
        
        .action-button:hover {
            color: var(--primary-blue);
        }
        
        /* 主内容区 */
        .main-content {
            flex: 1;
            display: grid;
            grid-template-columns: 60% 40%;
            overflow: hidden;
        }
        
        /* 左侧PPT预览区 */
        .preview-panel {
            background-color: var(--gray-200);
            padding: var(--spacing-md);
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }
        
        .slides-thumbnails {
            display: flex;
            gap: var(--spacing-xs);
            padding: var(--spacing-sm) 0;
            overflow-x: auto;
        }
        
        .slide-thumbnail {
            width: 120px;
            height: 67.5px; /* 16:9 比例 */
            background-color: white;
            border: 1px solid var(--gray-300);
            border-radius: 4px;
            cursor: pointer;
            position: relative;
            overflow: hidden;
        }
        
        .slide-thumbnail.active {
            border: 2px solid var(--primary-blue);
        }
        
        .slide-number {
            position: absolute;
            bottom: 4px;
            right: 4px;
            font-size: 10px;
            background-color: rgba(0, 0, 0, 0.5);
            color: white;
            padding: 2px 4px;
            border-radius: 2px;
        }
        
        .slide-preview {
            flex: 1;
            background-color: var(--gray-800);
            display: flex;
            align-items: center;
            justify-content: center;
            overflow: hidden;
            border-radius: 8px;
        }
        
        .slide-content {
            width: 80%;
            height: 80%;
            aspect-ratio: 16/9;
            background-color: white;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
            border-radius: 4px;
            position: relative;
        }
        
        /* 右侧编辑区 */
        .edit-panel {
            background-color: white;
            display: flex;
            flex-direction: column;
            border-left: 1px solid var(--gray-200);
        }
        
        .ai-assistant {
            background-color: var(--light-blue);
            padding: var(--spacing-md);
            border-bottom: 1px solid var(--gray-200);
        }
        
        .ai-header {
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
            margin-bottom: var(--spacing-sm);
            font-weight: 500;
            color: var(--ai-purple);
        }
        
        .ai-suggestions {
            margin-top: var(--spacing-sm);
        }
        
        .suggestion-item {
            margin-bottom: var(--spacing-xs);
            display: flex;
            align-items: flex-start;
            gap: var(--spacing-xs);
        }
        
        .ai-actions {
            display: flex;
            gap: var(--spacing-sm);
            margin-top: var(--spacing-md);
        }
        
        .ai-button {
            padding: var(--spacing-xs) var(--spacing-sm);
            border-radius: 4px;
            border: 1px solid var(--gray-300);
            background-color: white;
            cursor: pointer;
            font-size: 14px;
        }
        
        .ai-button.primary {
            background-color: var(--ai-purple);
            color: white;
            border: none;
        }
        
        .edit-tools {
            padding: var(--spacing-sm);
            border-bottom: 1px solid var(--gray-200);
            display: flex;
            gap: var(--spacing-sm);
        }
        
        .tool-button {
            padding: var(--spacing-xs) var(--spacing-sm);
            border-radius: 4px;
            border: none;
            background-color: var(--gray-100);
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 4px;
            font-size: 14px;
        }
        
        .tool-button:hover {
            background-color: var(--gray-200);
        }
        
        .edit-content {
            flex: 1;
            padding: var(--spacing-md);
            overflow-y: auto;
        }
        
        .property-group {
            margin-bottom: var(--spacing-md);
        }
        
        .property-title {
            font-weight: 500;
            margin-bottom: var(--spacing-xs);
            font-size: 14px;
            color: var(--gray-700);
        }
        
        .property-controls {
            display: flex;
            flex-direction: column;
            gap: var(--spacing-xs);
        }
        
        .input-control {
            padding: var(--spacing-xs) var(--spacing-sm);
            border: 1px solid var(--gray-300);
            border-radius: 4px;
            font-size: 14px;
        }
        
        .color-picker {
            display: flex;
            gap: var(--spacing-xs);
            flex-wrap: wrap;
        }
        
        .color-option {
            width: 24px;
            height: 24px;
            border-radius: 4px;
            cursor: pointer;
        }
        
        .color-option.selected {
            box-shadow: 0 0 0 2px white, 0 0 0 4px var(--primary-blue);
        }
        
        /* 模态框 */
        .modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: rgba(0, 0, 0, 0.5);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1000;
        }
        
        .modal {
            background-color: white;
            border-radius: 8px;
            width: 600px;
            max-width: 90%;
            max-height: 90vh;
            overflow-y: auto;
            box-shadow: 0 4px 24px rgba(0, 0, 0, 0.15);
        }
        
        .modal-header {
            padding: var(--spacing-md);
            border-bottom: 1px solid var(--gray-200);
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        
        .modal-title {
            font-weight: 500;
            font-size: 18px;
            display: flex;
            align-items: center;
            gap: var(--spacing-xs);
        }
        
        .modal-close {
            background: none;
            border: none;
            font-size: 24px;
            cursor: pointer;
            color: var(--gray-500);
        }
        
        .modal-body {
            padding: var(--spacing-md);
        }
        
        .modal-footer {
            padding: var(--spacing-md);
            border-top: 1px solid var(--gray-200);
            display: flex;
            justify-content: flex-end;
            gap: var(--spacing-sm);
        }
        
        .btn {
            padding: var(--spacing-xs) var(--spacing-md);
            border-radius: 4px;
            font-size: 14px;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: var(--spacing-xs);
        }
        
        .btn-secondary {
            background-color: white;
            border: 1px solid var(--gray-300);
            color: var(--gray-700);
        }
        
        .btn-primary {
            background-color: var(--primary-blue);
            border: none;
            color: white;
        }
        
        /* 导入向导 */
        .import-options {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: var(--spacing-sm);
            margin-bottom: var(--spacing-md);
        }
        
        .import-option {
            padding: var(--spacing-md);
            border: 1px solid var(--gray-300);
            border-radius: 8px;
            cursor: pointer;
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: var(--spacing-xs);
            text-align: center;
        }
        
        .import-option:hover {
            border-color: var(--primary-blue);
            background-color: var(--light-blue);
        }
        
        .import-option.selected {
            border-color: var(--primary-blue);
            background-color: var(--light-blue);
        }
        
        .import-icon {
            font-size: 24px;
            margin-bottom: var(--spacing-xs);
        }
        
        .import-title {
            font-weight: 500;
            font-size: 14px;
        }
        
        .import-desc {
            font-size: 12px;
            color: var(--gray-600);
        }
        
        .drop-area {
            border: 2px dashed var(--gray-300);
            border-radius: 8px;
            padding: var(--spacing-xl);
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            text-align: center;
            margin-bottom: var(--spacing-md);
        }
        
        .drop-area:hover {
            border-color: var(--primary-blue);
            background-color: var(--light-blue);
        }
        
        .supported-formats {
            font-size: 12px;
            color: var(--gray-600);
            margin-top: var(--spacing-sm);
        }
        
        /* 进度条 */
        .progress-container {
            margin: var(--spacing-md) 0;
        }
        
        .progress-bar {
            height: 8px;
            background-color: var(--gray-200);
            border-radius: 4px;
            overflow: hidden;
            margin-bottom: var(--spacing-sm);
        }
        
        .progress-fill {
            height: 100%;
            background-color: var(--primary-blue);
            width: 75%; /* 示例进度 */
        }
        
        .progress-steps {
            display: flex;
            flex-direction: column;
            gap: var(--spacing-xs);
            margin-top: var(--spacing-sm);
        }
        
        .progress-step {
            display: flex;
            align-items: center;
            gap: var(--spacing-xs);
            font-size: 14px;
        }
        
        .step-icon {
            width: 20px;
            height: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            font-size: 12px;
        }
        
        .step-icon.completed {
            background-color: var(--success-green);
            color: white;
        }
        
        .step-icon.in-progress {
            background-color: var(--primary-blue);
            color: white;
        }
        
        .step-icon.pending {
            background-color: var(--gray-300);
            color: var(--gray-600);
        }
        
        /* 模板选择 */
        .template-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-md);
        }
        
        .template-item {
            border: 1px solid var(--gray-300);
            border-radius: 8px;
            overflow: hidden;
            cursor: pointer;
            transition: transform 0.2s;
        }
        
        .template-item:hover {
            transform: translateY(-4px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }
        
        .template-item.selected {
            border: 2px solid var(--primary-blue);
        }
        
        .template-preview {
            height: 120px;
            background-color: var(--gray-100);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
        }
        
        .template-info {
            padding: var(--spacing-sm);
        }
        
        .template-name {
            font-weight: 500;
            margin-bottom: var(--spacing-xs);
            display: flex;
            align-items: center;
            gap: var(--spacing-xs);
        }
        
        .template-desc {
            font-size: 12px;
            color: var(--gray-600);
        }
        
        .template-rating {
            display: flex;
            gap: 2px;
            margin-top: var(--spacing-xs);
            font-size: 12px;
            color: var(--creative);
        }
    </style>
</head>
<body>
    <div class="app-container">
        <!-- 顶部导航栏 -->
        <header class="app-header">
            <div class="app-logo">
                <span>🎓</span> 智趣AI备课助手
            </div>
            <nav class="main-nav">
                <div class="nav-item">导入</div>
                <div class="nav-item">模板</div>
                <div class="nav-item">协作</div>
                <div class="nav-item">帮助</div>
            </nav>
            <div class="user-profile">
                <div class="user-avatar">张</div>
            </div>
        </header>
        
        <!-- 子导航栏 -->
        <div class="sub-header">
            <div class="breadcrumb">
                <div class="breadcrumb-item">我的备课</div>
                <div class="breadcrumb-separator">/</div>
                <div class="breadcrumb-item">明朝的建立</div>
                <div class="breadcrumb-separator">/</div>
                <div class="breadcrumb-item">PPT生成</div>
            </div>
            <div class="action-buttons">
                <button class="action-button" title="保存">💾</button>
                <button class="action-button" title="导出">📤</button>
                <button class="action-button" title="分享">🔗</button>
            </div>
        </div>
        
        <!-- 主内容区 -->
        <div class="main-content">
            <!-- 左侧PPT预览区 -->
            <div class="preview-panel">
                <div class="slides-thumbnails">
                    <div class="slide-thumbnail active">
                        <div class="slide-number">1</div>
                    </div>
                    <div class="slide-thumbnail">
                        <div class="slide-number">2</div>
                    </div>
                    <div class="slide-thumbnail">
                        <div class="slide-number">3</div>
                    </div>
                    <div class="slide-thumbnail">
                        <div class="slide-number">4</div>
                    </div>
                    <div class="slide-thumbnail">
                        <div class="slide-number">5</div>
                    </div>
                    <div class="slide-thumbnail">
                        <div class="slide-number">6</div>
                    </div>
                    <div class="slide-thumbnail">
                        <div class="slide-number">7</div>
                    </div>
                </div>
                <div class="slide-preview">
                    <div class="slide-content">
                        <!-- 当前幻灯片内容将在这里显示 -->
                        <div style="padding: 40px; height: 100%;">
                            <h1 style="font-size: 36px; margin-bottom: 20px; color: var(--dark-blue);">明朝的建立</h1>
                            <h3 style="font-size: 20px; color: var(--gray-600); margin-bottom: 40px;">初中历史七年级下册</h3>
                            <div style="display: flex; align-items: center; gap: 20px;">
                                <div style="flex: 1;">
                                    <p style="margin-bottom: 15px; font-size: 16px;">• 元朝末年社会背景</p>
                                    <p style="margin-bottom: 15px; font-size: 16px;">• 朱元璋的崛起</p>
                                    <p style="margin-bottom: 15px; font-size: 16px;">• 明朝的建立与巩固</p>
                                    <p style="font-size: 16px;">• 历史意义与影响</p>
                                </div>
                                <div style="width: 150px; height: 150px; background-color: var(--gray-200); border-radius: 4px; display: flex; align-items: center; justify-content: center; color: var(--gray-500);">
                                    图片占位
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 右侧编辑区 -->
            <div class="edit-panel">
                <div class="ai-assistant">
                    <div class="ai-header">
                        <span>🤖</span> AI助手建议
                    </div>
                    <div>根据您的教学设计，我为您生成了7页PPT。您可以：</div>
                    <div class="ai-suggestions">
                        <div class="suggestion-item">
                            <span>💡</span> 添加更多历史图片以增强视觉效果
                        </div>
                        <div class="suggestion-item">
                            <span>💡</span> 考虑在第4页添加朱元璋画像或相关历史地图
                        </div>
                    </div>
                    <div class="ai-actions">
                        <button class="ai-button">查看更多建议</button>
                        <button class="ai-button primary">一键优化</button>
                    </div>
                </div>
                
                <div class="edit-tools">
                    <button class="tool-button">
                        <span>T</span> 文本
                    </button>
                    <button class="tool-button">
                        <span>🖼️</span> 图片
                    </button>
                    <button class="tool-button">
                        <span>📊</span> 图表
                    </button>
                    <button class="tool-button">
                        <span>🔶</span> 形状
                    </button>
                </div>
                
                <div class="edit-content">
                    <div class="property-group">
                        <div class="property-title">幻灯片属性</div>
                        <div class="property-controls">
                            <input type="text" class="input-control" value="明朝的建立" placeholder="幻灯片标题">
                            <select class="input-control">
                                <option>标题页</option>
                                <option>内容页</option>
                                <option>图文页</option>
                                <option>总结页</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="property-group">
                        <div class="property-title">主题颜色</div>
                        <div class="color-picker">
                            <div class="color-option selected" style="background-color: var(--primary-blue);"></div>
                            <div class="color-option" style="background-color: var(--success-green);"></div>
                            <div class="color-option" style="background-color: var(--ai-purple);"></div>
                            <div class="color-option" style="background-color: var(--creative);"></div>
                            <div class="color-option" style="background-color: var(--media);"></div>
                            <div class="color-option" style="background-color: var(--analysis);"></div>
                        </div>
                    </div>
                    
                    <div class="property-group">
                        <div class="property-title">字体设置</div>
                        <div class="property-controls">
                            <select class="input-control">
                                <option>PingFang SC</option>
                                <option>Helvetica Neue</option>
                                <option>Arial</option>
                                <option>Microsoft YaHei</option>
                            </select>
                            <div style="display: flex; gap: var(--spacing-sm);">
                                <select class="input-control" style="width: 80px;">
                                    <option>12px</option>
                                    <option>14px</option>
                                    <option>16px</option>
                                    <option selected>18px</option>
                                    <option>20px</option>
                                    <option>24px</option>
                                </select>
                                <div style="display: flex; gap: 2px;">
                                    <button class="tool-button" style="flex: 1;">B</button>
                                    <button class="tool-button" style="flex: 1;">I</button>
                                    <button class="tool-button" style="flex: 1;">U</button>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="property-group">
                        <div class="property-title">历史图片素材</div>
                        <div style="display: grid; grid-template-columns: repeat(3, 1fr); gap: var(--spacing-xs);">
                            <div style="height: 60px; background-color: var(--gray-200); border-radius: 4px; cursor: pointer;"></div>
                            <div style="height: 60px; background-color: var(--gray-200); border-radius: 4px; cursor: pointer;"></div>
                            <div style="height: 60px; background-color: var(--gray-200); border-radius: 4px; cursor: pointer;"></div>
                            <div style="height: 60px; background-color: var(--gray-200); border-radius: 4px; cursor: pointer;"></div>
                            <div style="height: 60px; background-color: var(--gray-200); border-radius: 4px; cursor: pointer;"></div>
                            <div style="height: 60px; background-color: var(--gray-200); border-radius: 4px; cursor: pointer;"></div>
                        </div>
                        <button class="tool-button" style="width: 100%; margin-top: var(--spacing-xs);">
                            搜索更多图片
                        </button>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 模态框 - 导入向导 -->
        <div class="modal-overlay" id="importWizardModal" style="display: none;">
            <div class="modal">
                <div class="modal-header">
                    <div class="modal-title">
                        <span>📤</span> 从教学设计导入
                    </div>
                    <button class="modal-close">&times;</button>
                </div>
                <div class="modal-body">
                    <div class="import-options">
                        <div class="import-option selected">
                            <div class="import-icon">📝</div>
                            <div class="import-title">从教学设计导入</div>
                            <div class="import-desc">从已有的教学设计自动生成PPT</div>
                        </div>
                        <div class="import-option">
                            <div class="import-icon">📁</div>
                            <div class="import-title">上传PPT文件</div>
                            <div class="import-desc">上传已有PPT文件进行编辑</div>
                        </div>
                    </div>
                    
                    <div class="drop-area">
                        <div style="font-size: 24px; margin-bottom: var(--spacing-sm);">📄</div>
                        <div style="font-weight: 500; margin-bottom: var(--spacing-xs);">拖放文件到此处或点击上传</div>
                        <div style="font-size: 14px; color: var(--gray-600);">支持从已保存的教学设计中选择</div>
                        <div class="supported-formats">支持格式：DOCX, PDF, TXT</div>
                    </div>
                    
                    <div class="progress-container">
                        <div class="progress-bar">
                            <div class="progress-fill"></div>
                        </div>
                        <div>正在处理：75%</div>
                        <div class="progress-steps">
                            <div class="progress-step">
                                <div class="step-icon completed">✓</div>
                                <div>解析文档内容</div>
                            </div>
                            <div class="progress-step">
                                <div class="step-icon completed">✓</div>
                                <div>提取关键知识点</div>
                            </div>
                            <div class="progress-step">
                                <div class="step-icon in-progress">3</div>
                                <div>生成PPT结构</div>
                            </div>
                            <div class="progress-step">
                                <div class="step-icon pending">4</div>
                                <div>匹配历史图片素材</div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button class="btn btn-secondary">取消</button>
                    <button class="btn btn-primary">下一步</button>
                </div>
            </div>
        </div>
        
        <!-- 模态框 - 模板选择 -->
        <div class="modal-overlay" id="templateModal" style="display: none;">
            <div class="modal">
                <div class="modal-header">
                    <div class="modal-title">
                        <span>🎨</span> 选择PPT模板
                    </div>
                    <button class="modal-close">&times;</button>
                </div>
                <div class="modal-body">
                    <div class="template-grid">
                        <div class="template-item selected">
                            <div class="template-preview">🏛️</div>
                            <div class="template-info">
                                <div class="template-name">
                                    <span>🏛️</span> 历史经典
                                </div>
                                <div class="template-desc">适合历史主题的经典设计，庄重大气</div>
                                <div class="template-rating">
                                    <span>★</span><span>★</span><span>★</span><span>★</span><span>★</span>
                                </div>
                            </div>
                        </div>
                        <div class="template-item">
                            <div class="template-preview">🎭</div>
                            <div class="template-info">
                                <div class="template-name">
                                    <span>🎭</span> 文艺复兴
                                </div>
                                <div class="template-desc">融合文艺复兴元素，适合中世纪历史主题</div>
                                <div class="template-rating">
                                    <span>★</span><span>★</span><span>★</span><span>★</span><span>☆</span>
                                </div>
                            </div>
                        </div>
                        <div class="template-item">
                            <div class="template-preview">🏮</div>
                            <div class="template-info">
                                <div class="template-name">
                                    <span>🏮</span> 中国风
                                </div>
                                <div class="template-desc">中国传统元素设计，适合中国历史主题</div>
                                <div class="template-rating">
                                    <span>★</span><span>★</span><span>★</span><span>★</span><span>★</span>
                                </div>
                            </div>
                        </div>
                        <div class="template-item">
                            <div class="template-preview">🎨</div>
                            <div class="template-info">
                                <div class="template-name">
                                    <span>🎨</span> 现代简约
                                </div>
                                <div class="template-desc">简洁现代的设计风格，突出内容</div>
                                <div class="template-rating">
                                    <span>★</span><span>★</span><span>★</span><span>★</span><span>☆</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button class="btn btn-secondary">取消</button>
                    <button class="btn btn-primary">应用模板</button>
                </div>
            </div>
        </div>
        
        <!-- 模态框 - 导出选项 -->
        <div class="modal-overlay" id="exportModal" style="display: none;">
            <div class="modal">
                <div class="modal-header">
                    <div class="modal-title">
                        <span>📤</span> 导出PPT
                    </div>
                    <button class="modal-close">&times;</button>
                </div>
                <div class="modal-body">
                    <div class="import-options">
                        <div class="import-option selected">
                            <div class="import-icon">📊</div>
                            <div class="import-title">PowerPoint (.pptx)</div>
                            <div class="import-desc">标准PowerPoint格式，兼容Office</div>
                        </div>
                        <div class="import-option">
                            <div class="import-icon">📱</div>
                            <div class="import-title">PDF格式</div>
                            <div class="import-desc">适合分享和打印</div>
                        </div>
                    </div>
                    
                    <div class="property-group">
                        <div class="property-title">导出选项</div>
                        <div class="property-controls">
                            <label style="display: flex; align-items: center; gap: var(--spacing-xs);">
                                <input type="checkbox" checked> 包含演讲者备注
                            </label>
                            <label style="display: flex; align-items: center; gap: var(--spacing-xs);">
                                <input type="checkbox" checked> 高质量图片
                            </label>
                            <label style="display: flex; align-items: center; gap: var(--spacing-xs);">
                                <input type="checkbox"> 包含编辑历史
                            </label>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button class="btn btn-secondary">取消</button>
                    <button class="btn btn-primary">
                        <span>📥</span> 导出
                    </button>
                </div>
            </div>
        </div>
        
        <script>
            // 简单的交互逻辑示例
            document.addEventListener('DOMContentLoaded', function() {
                // 幻灯片缩略图点击事件
                const thumbnails = document.querySelectorAll('.slide-thumbnail');
                thumbnails.forEach(thumbnail => {
                    thumbnail.addEventListener('click', function() {
                        // 移除所有active类
                        thumbnails.forEach(t => t.classList.remove('active'));
                        // 为当前点击的缩略图添加active类
                        this.classList.add('active');
                        
                        // 这里可以添加加载对应幻灯片内容的逻辑
                    });
                });
                
                // 模态框显示/隐藏逻辑
                const showModal = (modalId) => {
                    document.getElementById(modalId).style.display = 'flex';
                };
                
                const hideModal = (modalId) => {
                    document.getElementById(modalId).style.display = 'none';
                };
                
                // 关闭按钮点击事件
                const closeButtons = document.querySelectorAll('.modal-close, .modal-footer .btn-secondary');
                closeButtons.forEach(button => {
                    button.addEventListener('click', function() {
                        const modal = this.closest('.modal-overlay');
                        modal.style.display = 'none';
                    });
                });
                
                // 示例：点击导出按钮显示导出模态框
                document.querySelector('.action-buttons button[title="导出"]').addEventListener('click', function() {
                    showModal('exportModal');
                });
                
                // 可以根据需要添加更多交互逻辑
            });
        </script>
    </div>
</body>
</html>
