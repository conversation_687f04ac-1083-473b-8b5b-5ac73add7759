<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PPT智能生成模块</title>
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <style>
        /* 基本样式重置 */
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif; color: #333; background-color: #f4f7f9; line-height: 1.6; padding: 20px; }

        /* 模态框统一样式 */
        .modal-overlay { position: fixed; top: 0; left: 0; right: 0; bottom: 0; background: rgba(0,0,0,0.5); display: flex; align-items: center; justify-content: center; z-index: 1000; }
        .modal { background: white; border-radius: 8px; box-shadow: 0 4px 15px rgba(0,0,0,0.2); width: 100%; max-width: 700px; overflow: hidden; }
        .modal-header { padding: 20px; border-bottom: 1px solid #eee; display: flex; justify-content: space-between; align-items: center; background-color: #f8f9fa; }
        .modal-title { font-size: 20px; font-weight: 600; }
        .modal-close { background: none; border: none; font-size: 24px; cursor: pointer; color: #777; }
        .modal-body { padding: 20px; max-height: 70vh; overflow-y: auto; }
        .modal-footer { padding: 20px; border-top: 1px solid #eee; text-align: right; background-color: #f8f9fa; }

        /* 表单元素 */
        .form-group { margin-bottom: 20px; }
        .form-label { display: block; font-weight: 500; margin-bottom: 8px; }
        .form-select, .form-input[type="checkbox"] { padding: 10px; border: 1px solid #ddd; border-radius: 4px; font-size: 16px; width: 100%; }
        .form-input[type="checkbox"] { width: auto; margin-right: 8px; }
        
        /* 按钮 */
        .btn { padding: 10px 20px; border-radius: 4px; font-weight: 500; border: none; cursor: pointer; transition: background-color 0.2s ease; font-size: 16px; }
        .btn-primary { background-color: #007bff; color: white; }
        .btn-primary:hover { background-color: #0056b3; }
        .btn-primary:disabled { background-color: #a0c7e4; cursor: not-allowed; }
        .btn-secondary { background-color: #6c757d; color: white; margin-right: 10px; }
        .btn-secondary:hover { background-color: #545b62; }

        /* 模板选择 */
        .template-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin-bottom: 20px; }
        .template-item { border: 2px solid #eee; border-radius: 8px; padding: 15px; cursor: pointer; transition: all 0.2s ease; text-align: center; }
        .template-item:hover { border-color: #007bff; box-shadow: 0 2px 8px rgba(0,123,255,0.1); }
        .template-item.selected { border-color: #007bff; background-color: #e7f3ff; }
        .template-icon { font-size: 24px; margin-bottom: 8px; }
        .template-name { font-weight: 600; margin-bottom: 4px; }
        .template-desc { font-size: 13px; color: #555; }

        /* 提示信息 */
        .alert { padding: 15px; border-radius: 4px; margin-bottom: 20px; border: 1px solid transparent; display: flex; align-items: center; gap: 10px; }
        .alert-info { background-color: #e7f3ff; border-color: #b3d7ff; color: #004085; }
        .alert-info span { font-size: 20px; }
        
        /* 加载与成功提示 */
        .toast { position: fixed; top: 20px; right: 20px; background-color: #333; color: white; padding: 15px 25px; border-radius: 5px; z-index: 2000; font-size: 16px; box-shadow: 0 2px 10px rgba(0,0,0,0.2); }
        .toast.success { background-color: #28a745; }
        .toast.error { background-color: #dc3545; }
    </style>
</head>
<body>
    <div id="pptApp">
        <!-- PPT生成模态框 (始终显示，因为这是独立页面) -->
        <div class="modal-overlay">
            <div class="modal" style="max-width: 800px;">
                <div class="modal-header">
                    <h3 class="modal-title">🎯 生成PPT课件</h3>
                    <!-- <button class="modal-close" @click="showPPTModal = false">&times;</button> --> <!-- 在独立页面中不需要关闭按钮 -->
                </div>
                <div class="modal-body">
                    <div class="alert alert-info">
                        <span>💡</span>
                        <div>
                            <strong>智能提示</strong>
                            <p style="margin: 4px 0 0 0;">请选择PPT模板、页面数量等选项，AI将根据您的教学设计内容（此处假设已存在或通过API获取）生成精美的PPT课件。</p>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">🎨 选择PPT模板风格</label>
                        <div class="template-grid">
                            <div class="template-item" :class="{ selected: pptTemplate === 'academic' }" @click="selectTemplate('academic')">
                                <div class="template-icon">📚</div>
                                <div class="template-name">经典学术风</div>
                                <div class="template-desc">传统严谨，适合正式教学</div>
                            </div>
                            <div class="template-item" :class="{ selected: pptTemplate === 'modern' }" @click="selectTemplate('modern')">
                                <div class="template-icon">🎨</div>
                                <div class="template-name">现代简约风</div>
                                <div class="template-desc">简洁大方，视觉效果佳</div>
                            </div>
                            <div class="template-item" :class="{ selected: pptTemplate === 'creative' }" @click="selectTemplate('creative')">
                                <div class="template-icon">🌈</div>
                                <div class="template-name">创意活泼风</div>
                                <div class="template-desc">色彩丰富，吸引学生注意</div>
                            </div>
                            <div class="template-item" :class="{ selected: pptTemplate === 'business' }" @click="selectTemplate('business')">
                                <div class="template-icon">💼</div>
                                <div class="template-name">商务专业风</div>
                                <div class="template-desc">专业大气，适合公开课</div>
                            </div>
                        </div>
                    </div>
                    
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
                        <div class="form-group">
                            <label class="form-label">📄 页面数量</label>
                            <select class="form-select" v-model="pptPages">
                                <option value="auto">自动（根据内容）</option>
                                <option value="10">10-15页</option>
                                <option value="15">15-20页</option>
                                <option value="20">20-25页</option>
                                <option value="25">25-30页</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label class="form-label">🎵 动画效果 (示例)</label>
                            <select class="form-select" v-model="pptAnimation">
                                <option value="simple">简单过渡</option>
                                <option value="elegant">优雅动画</option>
                                <option value="dynamic">动态效果</option>
                                <option value="none">无动画</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">✨ 特殊功能</label>
                        <div style="display: grid; gap: 12px; margin-top: 8px;">
                            <label style="display: flex; align-items: center; gap: 8px; font-weight: normal;">
                                <input type="checkbox" v-model="includeExercises">
                                包含课堂练习页面
                            </label>
                            <label style="display: flex; align-items: center; gap: 8px; font-weight: normal;">
                                <input type="checkbox" v-model="includeTimeline"> <!-- 新增数据绑定 -->
                                自动生成历史时间线
                            </label>
                            <label style="display: flex; align-items: center; gap: 8px; font-weight: normal;">
                                <input type="checkbox" v-model="includeQuizzes"> <!-- 新增数据绑定 -->
                                嵌入互动问答环节
                            </label>
                            <label style="display: flex; align-items: center; gap: 8px; font-weight: normal;">
                                <input type="checkbox" v-model="includeSummary"> <!-- 新增数据绑定 -->
                                添加课程总结页面
                            </label>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <!-- <button class="btn btn-secondary" @click="showPPTModal = false">取消</button> --> <!-- 在独立页面中不需要 -->
                    <button class="btn btn-primary" @click="generatePPT" :disabled="!pptTemplate || isLoading">
                        <span v-if="isLoading">🔄 生成中...</span>
                        <span v-else>🎯 生成PPT课件</span>
                    </button>
                </div>
            </div>
        </div>

        <!-- 提示消息 -->
        <div v-if="toast.message" :class="['toast', toast.type]">
            {{ toast.message }}
        </div>
    </div>

    <script>
        const { createApp, ref, reactive } = Vue;

        createApp({
            data() {
                return {
                    // PPT 生成相关
                    pptTemplate: null, // 初始化为null，强制用户选择
                    pptPages: 'auto',
                    pptAnimation: 'simple', // 示例数据
                    includeExercises: true,
                    includeTimeline: false, // 新增数据
                    includeQuizzes: false,  // 新增数据
                    includeSummary: true,   // 新增数据
                    
                    isLoading: false, // 处理加载状态
                    toast: { // 用于显示提示信息
                        message: '',
                        type: '' // 'success' 或 'error'
                    },
                    // 假设的教学设计ID，实际应用中应从URL参数、父组件或全局状态获取
                    teachingDesignId: 'sample_design_123', 
                    generatedPPTResource: null // 存储生成的PPT信息
                };
            },
            methods: {
                selectTemplate(templateName) {
                    this.pptTemplate = templateName;
                },
                async generatePPT() {
                    if (!this.pptTemplate) {
                        this.showToast('请先选择一个PPT模板风格。', 'error');
                        return;
                    }

                    this.isLoading = true;
                    this.generatedPPTResource = null; // 重置之前的生成结果
                    this.showToast('PPT正在生成中，请稍候...', 'info');

                    // 模拟API调用
                    try {
                        // 实际场景: 调用后端API，传递 this.teachingDesignId 和PPT选项
                        // const response = await fetch('/api/generate-ppt', {
                        //     method: 'POST',
                        //     headers: { 'Content-Type': 'application/json' },
                        //     body: JSON.stringify({
                        //         teachingDesignId: this.teachingDesignId,
                        //         template: this.pptTemplate,
                        //         pages: this.pptPages,
                        //         animation: this.pptAnimation,
                        //         features: {
                        //             exercises: this.includeExercises,
                        //             timeline: this.includeTimeline,
                        //             quizzes: this.includeQuizzes,
                        //             summary: this.includeSummary
                        //         }
                        //     })
                        // });

                        // if (!response.ok) {
                        //     const errorData = await response.json();
                        //     throw new Error(errorData.message || 'PPT生成失败，请重试。');
                        // }
                        // const result = await response.json(); // { pptUrl: '...', resourceId: '...' }

                        // 模拟成功返回
                        await new Promise(resolve => setTimeout(resolve, 2500)); // 模拟网络延迟
                        const mockResult = {
                            id: Date.now(),
                            title: `基于设计 ${this.teachingDesignId} 生成的PPT (${this.pptTemplate})`,
                            type: 'PPT课件',
                            icon: '🎯',
                            grade: '通用', // 示例，实际可从教学设计中获取
                            keywords: ['PPT', this.pptTemplate, this.teachingDesignId],
                            description: `包含 ${this.pptPages} 页，练习题: ${this.includeExercises}, 时间线: ${this.includeTimeline}, 问答: ${this.includeQuizzes}, 总结: ${this.includeSummary}`,
                            url: `https://example.com/generated_ppt_${Date.now()}.pptx`, // 模拟PPT链接
                            date: new Date().toLocaleString()
                        };
                        this.generatedPPTResource = mockResult;
                        
                        // 更新应用状态或发送到父应用（如果嵌入）
                        // console.log('PPT生成成功:', this.generatedPPTResource);
                        this.showToast('PPT生成成功！已保存（模拟）。', 'success');

                    } catch (error) {
                        console.error('PPT生成错误:', error);
                        this.showToast(error.message || 'PPT生成过程中发生错误，请检查网络或联系管理员。', 'error');
                    } finally {
                        this.isLoading = false;
                    }
                },
                showToast(message, type = 'info') {
                    this.toast.message = message;
                    this.toast.type = type;
                    setTimeout(() => {
                        this.toast.message = '';
                        this.toast.type = '';
                    }, 4000); // 4秒后自动消失
                }
            },
            mounted() {
                this.showToast('PPT智能生成模块已加载。', 'info');
                // 可以在这里预加载教学设计信息或进行其他初始化操作
                // console.log("PPT模块的教学设计ID:", this.teachingDesignId);
            }
        }).mount('#pptApp');
    </script>
</body>
</html> 