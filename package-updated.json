{"name": "zhiqu-ai", "version": "0.1.0", "description": "智趣AI - 初中历史智能备课系统", "private": true, "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs --fix --ignore-path .gitignore"}, "dependencies": {"vue": "^3.3.4", "vue-router": "^4.2.4", "pinia": "^2.1.6", "axios": "^1.5.0"}, "devDependencies": {"@stagewise/toolbar": "^0.2.1", "@vitejs/plugin-vue": "^4.2.3", "vite": "^4.4.5", "sass": "^1.66.1", "eslint": "^8.47.0", "eslint-plugin-vue": "^9.17.0", "@vue/eslint-config-prettier": "^8.0.0", "prettier": "^3.0.2"}}