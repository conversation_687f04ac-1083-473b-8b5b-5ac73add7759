# 智趣AI系统 Demo 启动指南

## 📋 项目概述

基于设计文档创建的智趣AI初中历史智能备课系统Demo，展示核心功能和用户界面。

## 🚀 快速开始

### 1. 环境要求
- Node.js >= 16.0.0
- npm >= 8.0.0 或 yarn >= 1.22.0

### 2. 安装依赖

首先，更新package.json文件：
```bash
# 备份原文件并使用新的配置
mv package.json package-old.json
mv package-updated.json package.json
```

然后安装依赖：
```bash
# 使用npm
npm install

# 或使用yarn
yarn install
```

### 3. 启动开发服务器
```bash
# 使用npm
npm run dev

# 或使用yarn
yarn dev
```

访问 http://localhost:3000 查看Demo

## 📁 项目结构

```
src/
├── components/          # 组件目录
│   ├── common/         # 通用组件
│   └── layout/         # 布局组件
├── views/              # 页面组件
├── stores/             # 状态管理
├── router/             # 路由配置
├── styles/             # 样式文件
├── utils/              # 工具函数
└── main.js            # 入口文件
```

## 🎯 Demo功能特性

### ✅ 已实现功能
1. **基础架构**
   - Vue 3 + Vite 开发环境
   - Vue Router 路由管理
   - Pinia 状态管理
   - SCSS 样式系统

2. **界面组件**
   - 响应式导航栏
   - 用户信息管理
   - 统一的设计系统

3. **页面结构**
   - 工作台页面框架
   - 教学设计页面框架
   - PPT生成页面框架
   - 智能组卷页面框架
   - 资源库页面框架
   - 个人中心页面框架

### 🔄 待完善功能
1. **页面内容实现**
   - 各页面的具体功能组件
   - 表单交互逻辑
   - 数据展示组件

2. **AI功能集成**
   - 模拟AI生成接口
   - 加载状态处理
   - 错误处理机制

3. **数据管理**
   - 本地存储优化
   - 数据持久化
   - 缓存策略

## 🛠 开发指南

### 添加新页面
1. 在 `src/views/` 创建页面组件
2. 在 `src/router/index.js` 添加路由配置
3. 更新导航菜单（如需要）

### 添加新组件
1. 在 `src/components/` 相应目录创建组件
2. 使用统一的样式变量和混入
3. 遵循组件命名规范

### 样式开发
- 使用SCSS预处理器
- 遵循BEM命名规范
- 使用CSS变量保持一致性
- 响应式设计优先

## 📱 响应式设计

支持以下断点：
- Mobile: < 768px
- Tablet: 768px - 1023px
- Desktop: >= 1024px
- Large Desktop: >= 1280px

## 🎨 设计系统

### 色彩规范
- 主色调：#6366f1 (蓝紫色)
- 辅助色：#06b6d4 (青色)
- 强调色：#10b981 (绿色)
- 状态色：成功、警告、错误、信息

### 间距系统
- xs: 4px
- sm: 8px
- md: 16px
- lg: 24px
- xl: 32px

### 圆角规范
- sm: 4px
- md: 8px
- lg: 12px
- xl: 16px

## 🔧 构建部署

### 构建生产版本
```bash
npm run build
```

### 预览构建结果
```bash
npm run preview
```

## 📝 开发注意事项

1. **代码规范**
   - 使用ESLint进行代码检查
   - 遵循Vue 3 Composition API规范
   - 组件使用TypeScript类型注解（可选）

2. **性能优化**
   - 路由懒加载
   - 组件按需导入
   - 图片资源优化

3. **用户体验**
   - 加载状态提示
   - 错误边界处理
   - 无障碍访问支持

## 🐛 常见问题

### Q: 启动时出现依赖错误
A: 删除 `node_modules` 和 `package-lock.json`，重新安装依赖

### Q: 样式不生效
A: 检查SCSS文件导入路径，确保变量文件正确引入

### Q: 路由跳转异常
A: 检查路由配置和组件导入路径

## 📞 技术支持

如遇到问题，请检查：
1. Node.js版本是否符合要求
2. 依赖是否正确安装
3. 端口是否被占用
4. 浏览器控制台错误信息

## 🎯 下一步计划

1. **第一阶段**：完善页面组件实现
2. **第二阶段**：集成模拟AI接口
3. **第三阶段**：添加数据持久化
4. **第四阶段**：性能优化和测试

---

**注意**：这是一个Demo版本，主要用于展示界面设计和基础功能架构。生产环境需要完善后端API集成、安全认证、数据库连接等功能。
