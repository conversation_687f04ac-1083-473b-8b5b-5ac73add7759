<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智趣AI - 初中历史智能备课系统</title>
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        :root {
            --primary-color: #2563eb;
            --primary-dark: #1e40af;
            --primary-light: #3b82f6;
            --primary-bg: #eff6ff;
            --secondary-color: #06b6d4;
            --secondary-light: #22d3ee;
            --secondary-bg: #ecfeff;
            --accent-color: #10b981;
            --accent-light: #34d399;
            --gray-900: #111827;
            --gray-800: #1f2937;
            --gray-700: #374151;
            --gray-600: #4b5563;
            --gray-500: #6b7280;
            --gray-400: #9ca3af;
            --gray-300: #d1d5db;
            --gray-200: #e5e7eb;
            --gray-100: #f3f4f6;
            --gray-50: #f9fafb;
            --font-primary: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", sans-serif;
            --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
            --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1);
            --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1);
            --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1);
            --radius-sm: 4px;
            --radius-md: 8px;
            --radius-lg: 12px;
            --radius-xl: 16px;
        }
        
        body {
            font-family: var(--font-primary);
            color: var(--gray-900);
            background-color: var(--gray-50);
            line-height: 1.6;
        }
        
        /* 布局 */
        .app-container {
            min-height: 100vh;
            display: flex;
            flex-direction: column;
        }
        
        .main-header {
            background: white;
            box-shadow: var(--shadow-sm);
            position: sticky;
            top: 0;
            z-index: 100;
        }
        
        .header-content {
            max-width: 1400px;
            margin: 0 auto;
            padding: 0 20px;
            height: 64px;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        
        .logo {
            font-size: 24px;
            font-weight: 700;
            color: var(--primary-color);
            text-decoration: none;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .nav-menu {
            display: flex;
            gap: 32px;
            list-style: none;
        }
        
        .nav-link {
            color: var(--gray-700);
            text-decoration: none;
            font-weight: 500;
            padding: 8px 16px;
            border-radius: var(--radius-md);
            transition: all 0.3s ease;
            cursor: pointer;
        }
        
        .nav-link:hover {
            color: var(--primary-color);
            background: var(--primary-bg);
        }
        
        .nav-link.active {
            color: var(--primary-color);
            background: var(--primary-bg);
        }
        
        .user-info {
            display: flex;
            align-items: center;
            gap: 12px;
        }
        
        .user-avatar {
            width: 40px;
            height: 40px;
            background: var(--primary-color);
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
        }
        
        .main-content {
            flex: 1;
            max-width: 1400px;
            margin: 0 auto;
            padding: 40px 20px;
            width: 100%;
        }
        
        /* 卡片 */
        .card {
            background: white;
            border-radius: var(--radius-lg);
            padding: 32px;
            box-shadow: var(--shadow-md);
            margin-bottom: 24px;
            transition: all 0.3s ease;
        }
        
        .card:hover {
            box-shadow: var(--shadow-xl);
            transform: translateY(-2px);
        }
        
        /* 按钮 */
        .btn {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            padding: 12px 24px;
            border-radius: var(--radius-md);
            font-weight: 500;
            border: none;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            font-size: 16px;
            font-family: inherit;
        }
        
        .btn-primary {
            background: var(--primary-color);
            color: white;
        }
        
        .btn-primary:hover {
            background: var(--primary-dark);
            box-shadow: var(--shadow-md);
            transform: translateY(-1px);
        }
        
        .btn-secondary {
            background: white;
            color: var(--primary-color);
            border: 2px solid var(--primary-color);
        }
        
        .btn-secondary:hover {
            background: var(--primary-bg);
        }
        
        .btn-success {
            background: var(--accent-color);
            color: white;
        }
        
        .btn-success:hover {
            background: #059669;
            box-shadow: var(--shadow-md);
        }
        
        .btn-sm {
            padding: 8px 16px;
            font-size: 14px;
        }
        
        /* 输入框 */
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-label {
            display: block;
            font-weight: 500;
            margin-bottom: 8px;
            color: var(--gray-700);
        }
        
        .form-input,
        .form-select,
        .form-textarea {
            width: 100%;
            padding: 12px 16px;
            border: 2px solid var(--gray-300);
            border-radius: var(--radius-md);
            font-size: 16px;
            transition: all 0.3s ease;
            font-family: inherit;
        }
        
        .form-textarea {
            min-height: 120px;
            resize: vertical;
        }
        
        .form-input:focus,
        .form-select:focus,
        .form-textarea:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
        }
        
        /* 网格 */
        .grid {
            display: grid;
            gap: 24px;
        }
        
        .grid-3 {
            grid-template-columns: repeat(3, 1fr);
        }
        
        .grid-2 {
            grid-template-columns: repeat(2, 1fr);
        }
        
        /* 标签 */
        .tag {
            display: inline-flex;
            align-items: center;
            padding: 6px 12px;
            background: var(--secondary-bg);
            color: var(--secondary-color);
            border-radius: var(--radius-sm);
            font-size: 14px;
            font-weight: 500;
            margin-right: 8px;
            margin-bottom: 8px;
        }
        
        /* 步骤指示器 */
        .steps {
            display: flex;
            justify-content: space-between;
            margin-bottom: 40px;
        }
        
        .step {
            flex: 1;
            text-align: center;
            position: relative;
        }
        
        .step:not(:last-child)::after {
            content: '';
            position: absolute;
            top: 20px;
            left: 50%;
            width: 100%;
            height: 2px;
            background: var(--gray-300);
            z-index: -1;
        }
        
        .step.active:not(:last-child)::after,
        .step.completed:not(:last-child)::after {
            background: var(--primary-color);
        }
        
        .step-number {
            width: 40px;
            height: 40px;
            background: white;
            border: 2px solid var(--gray-300);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 8px;
            font-weight: 600;
            color: var(--gray-500);
        }
        
        .step.active .step-number,
        .step.completed .step-number {
            background: var(--primary-color);
            border-color: var(--primary-color);
            color: white;
        }
        
        .step-label {
            font-size: 14px;
            color: var(--gray-600);
        }
        
        .step.active .step-label {
            color: var(--primary-color);
            font-weight: 500;
        }
        
        /* 模态框 */
        .modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.5);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1000;
            padding: 20px;
        }
        
        .modal {
            background: white;
            border-radius: var(--radius-lg);
            box-shadow: var(--shadow-xl);
            width: 100%;
            max-width: 600px;
            max-height: 90vh;
            overflow: hidden;
            display: flex;
            flex-direction: column;
        }
        
        .modal-header {
            padding: 24px;
            border-bottom: 1px solid var(--gray-200);
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        
        .modal-title {
            font-size: 20px;
            font-weight: 600;
            color: var(--gray-900);
        }
        
        .modal-close {
            background: none;
            border: none;
            font-size: 24px;
            cursor: pointer;
            color: var(--gray-400);
            padding: 4px;
        }
        
        .modal-body {
            padding: 24px;
            overflow-y: auto;
            flex: 1;
        }
        
        .modal-footer {
            padding: 16px 24px;
            background: var(--gray-50);
            display: flex;
            justify-content: flex-end;
            gap: 12px;
        }
        
        /* 加载动画 */
        .loading {
            text-align: center;
            padding: 40px;
        }
        
        .loading-spinner {
            display: inline-block;
            width: 48px;
            height: 48px;
            border: 4px solid var(--gray-200);
            border-top-color: var(--primary-color);
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            to { transform: rotate(360deg); }
        }
        
        .loading-text {
            margin-top: 16px;
            color: var(--gray-600);
        }
        
        /* 进度条 */
        .progress-bar {
            width: 100%;
            height: 8px;
            background: var(--gray-200);
            border-radius: 4px;
            overflow: hidden;
            margin-top: 8px;
        }
        
        .progress-fill {
            height: 100%;
            background: var(--primary-color);
            border-radius: 4px;
            transition: width 0.3s ease;
        }
        
        /* 空状态 */
        .empty-state {
            text-align: center;
            padding: 60px;
            color: var(--gray-500);
        }
        
        .empty-icon {
            font-size: 64px;
            margin-bottom: 16px;
            opacity: 0.5;
        }
        
        /* 快速操作卡片 */
        .action-card {
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .action-card:hover {
            transform: translateY(-4px);
        }
        
        .action-icon {
            font-size: 48px;
            margin-bottom: 16px;
        }
        
        .action-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 8px;
        }
        
        .action-desc {
            color: var(--gray-600);
            font-size: 14px;
        }
        
        /* 资源卡片 */
        .resource-card {
            display: flex;
            gap: 20px;
            align-items: start;
        }
        
        .resource-icon {
            width: 80px;
            height: 80px;
            background: var(--gray-100);
            border-radius: var(--radius-md);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 32px;
            flex-shrink: 0;
        }
        
        .resource-content {
            flex: 1;
        }
        
        .resource-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 8px;
        }
        
        .resource-meta {
            margin-bottom: 8px;
        }
        
        .resource-desc {
            font-size: 14px;
            color: var(--gray-600);
            margin-bottom: 12px;
        }
        
        .resource-actions {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .resource-date {
            font-size: 14px;
            color: var(--gray-500);
        }
        
        /* 模板选择 */
        .template-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 20px;
        }
        
        .template-item {
            border: 2px solid var(--gray-300);
            border-radius: var(--radius-lg);
            padding: 24px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .template-item:hover {
            border-color: var(--primary-light);
            box-shadow: var(--shadow-md);
        }
        
        .template-item.selected {
            border-color: var(--primary-color);
            background: var(--primary-bg);
        }
        
        .template-icon {
            font-size: 32px;
            margin-bottom: 12px;
        }
        
        .template-name {
            font-weight: 600;
            margin-bottom: 8px;
        }
        
        .template-desc {
            font-size: 14px;
            color: var(--gray-600);
        }
        
        /* 提示框 */
        .alert {
            padding: 16px 20px;
            border-radius: var(--radius-md);
            margin-bottom: 16px;
            display: flex;
            align-items: center;
            gap: 12px;
        }
        
        .alert-success {
            background: #d1fae5;
            color: #065f46;
            border: 1px solid #6ee7b7;
        }
        
        .alert-info {
            background: var(--primary-bg);
            color: var(--primary-dark);
            border: 1px solid var(--primary-light);
        }
        
        /* 编辑器 */
        .editor {
            border: 2px solid var(--gray-300);
            border-radius: var(--radius-md);
            overflow: hidden;
        }
        
        .editor-toolbar {
            background: var(--gray-50);
            padding: 12px;
            border-bottom: 1px solid var(--gray-300);
            display: flex;
            gap: 8px;
        }
        
        .editor-btn {
            background: white;
            border: 1px solid var(--gray-300);
            padding: 6px 12px;
            border-radius: var(--radius-sm);
            cursor: pointer;
            font-size: 14px;
            transition: all 0.2s ease;
        }
        
        .editor-btn:hover {
            background: var(--gray-50);
            border-color: var(--gray-400);
        }
        
        .editor-content {
            min-height: 400px;
            padding: 20px;
            line-height: 1.8;
        }
        
        .editor-content:focus {
            outline: none;
        }
        
        /* 响应式 */
        @media (max-width: 768px) {
            .grid-3 {
                grid-template-columns: 1fr;
            }
            
            .grid-2 {
                grid-template-columns: 1fr;
            }
            
            .template-grid {
                grid-template-columns: 1fr;
            }
            
            .nav-menu {
                display: none;
            }
            
            .resource-card {
                flex-direction: column;
            }
            
            .resource-icon {
                width: 60px;
                height: 60px;
            }
        }
        
        /* 动画 */
        .fade-enter-active,
        .fade-leave-active {
            transition: opacity 0.3s ease;
        }
        
        .fade-enter-from,
        .fade-leave-to {
            opacity: 0;
        }
        
        .slide-enter-active,
        .slide-leave-active {
            transition: all 0.3s ease;
        }
        
        .slide-enter-from {
            transform: translateX(20px);
            opacity: 0;
        }
        
        .slide-leave-to {
            transform: translateX(-20px);
            opacity: 0;
        }
    </style>
</head>
<body>
    <div id="app">
        <div class="app-container">
            <!-- 头部导航 -->
            <header class="main-header">
                <div class="header-content">
                    <a href="#" class="logo" @click="currentView = 'dashboard'">
                        <span>🎓</span>
                        <span>智趣AI</span>
                    </a>
                    <nav>
                        <ul class="nav-menu">
                            <li>
                                <a class="nav-link" :class="{ active: currentView === 'dashboard' }" 
                                   @click="currentView = 'dashboard'">工作台</a>
                            </li>
                            <li>
                                <a class="nav-link" :class="{ active: currentView === 'teaching-design' }" 
                                   @click="currentView = 'teaching-design'">教学设计</a>
                            </li>
                            <li>
                                <a class="nav-link" :class="{ active: currentView === 'resources' }" 
                                   @click="currentView = 'resources'">资源库</a>
                            </li>
                            <li>
                                <a class="nav-link" :class="{ active: currentView === 'profile' }" 
                                   @click="currentView = 'profile'">个人中心</a>
                            </li>
                        </ul>
                    </nav>
                    <div class="user-info">
                        <div class="user-avatar">张</div>
                        <span>张老师</span>
                    </div>
                </div>
            </header>

            <!-- 主内容区 -->
            <main class="main-content">
                <transition name="fade" mode="out-in">
                    <!-- 工作台 -->
                    <div v-if="currentView === 'dashboard'" key="dashboard">
                        <h1 style="margin-bottom: 32px;">欢迎回来，张老师！</h1>
                        
                        <!-- 快速操作 -->
                        <h2 style="font-size: 24px; margin-bottom: 20px;">快速开始</h2>
                        <div class="grid grid-3" style="margin-bottom: 40px;">
                            <div class="card action-card" @click="startTeachingDesign">
                                <div class="action-icon">📝</div>
                                <h3 class="action-title">智能教学设计</h3>
                                <p class="action-desc">AI辅助生成教学方案</p>
                            </div>
                            <div class="card action-card" @click="showPPTModal = true">
                                <div class="action-icon">🎯</div>
                                <h3 class="action-title">PPT快速生成</h3>
                                <p class="action-desc">一键转换精美课件</p>
                            </div>
                            <div class="card action-card" @click="showQuizModal = true">
                                <div class="action-icon">📊</div>
                                <h3 class="action-title">智能组卷</h3>
                                <p class="action-desc">自动生成配套习题</p>
                            </div>
                        </div>

                        <!-- 最近使用 -->
                        <h2 style="font-size: 24px; margin-bottom: 20px;">最近使用</h2>
                        <div class="card">
                            <div v-if="recentResources.length === 0" class="empty-state">
                                <div class="empty-icon">📂</div>
                                <p>暂无最近使用的资源</p>
                                <button class="btn btn-primary" style="margin-top: 16px;" @click="startTeachingDesign">
                                    创建第一个教学设计
                                </button>
                            </div>
                            <div v-else>
                                <div v-for="(resource, index) in recentResources" :key="index" 
                                     class="resource-card" style="padding: 20px; border-bottom: 1px solid var(--gray-200);"
                                     :style="{ 'border-bottom': index === recentResources.length - 1 ? 'none' : '' }">
                                    <div class="resource-icon" :style="{ background: getResourceColor(resource.type) }">
                                        {{ resource.icon }}
                                    </div>
                                    <div class="resource-content">
                                        <h4 class="resource-title">{{ resource.title }}</h4>
                                        <div class="resource-meta">
                                            <span class="tag">{{ resource.grade }}</span>
                                            <span class="tag">{{ resource.type }}</span>
                                        </div>
                                        <p class="resource-desc">{{ resource.description }}</p>
                                        <div class="resource-actions">
                                            <span class="resource-date">{{ resource.date }}</span>
                                            <div style="display: flex; gap: 8px;">
                                                <button class="btn btn-secondary btn-sm">预览</button>
                                                <button class="btn btn-primary btn-sm">编辑</button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 教学设计 -->
                    <div v-else-if="currentView === 'teaching-design'" key="teaching-design">
                        <h1 style="margin-bottom: 32px;">智能教学设计</h1>
                        
                        <div class="card">
                            <!-- 步骤指示器 -->
                            <div class="steps">
                                <div class="step" :class="{ active: designStep === 1, completed: designStep > 1 }">
                                    <div class="step-number">{{ designStep > 1 ? '✓' : '1' }}</div>
                                    <div class="step-label">选择模板</div>
                                </div>
                                <div class="step" :class="{ active: designStep === 2, completed: designStep > 2 }">
                                    <div class="step-number">{{ designStep > 2 ? '✓' : '2' }}</div>
                                    <div class="step-label">填写信息</div>
                                </div>
                                <div class="step" :class="{ active: designStep === 3, completed: designStep > 3 }">
                                    <div class="step-number">{{ designStep > 3 ? '✓' : '3' }}</div>
                                    <div class="step-label">AI生成</div>
                                </div>
                                <div class="step" :class="{ active: designStep === 4 }">
                                    <div class="step-number">4</div>
                                    <div class="step-label">编辑优化</div>
                                </div>
                            </div>

                            <!-- 步骤内容 -->
                            <transition name="slide" mode="out-in">
                                <!-- 步骤1：选择模板 -->
                                <div v-if="designStep === 1" key="step1">
                                    <h2 style="margin-bottom: 24px;">选择教学设计模板</h2>
                                    <div class="template-grid">
                                        <div v-for="template in templates" :key="template.id"
                                             class="template-item" 
                                             :class="{ selected: selectedTemplate === template.id }"
                                             @click="selectedTemplate = template.id">
                                            <div class="template-icon">{{ template.icon }}</div>
                                            <h3 class="template-name">{{ template.name }}</h3>
                                            <p class="template-desc">{{ template.description }}</p>
                                        </div>
                                    </div>
                                    <div style="margin-top: 40px; display: flex; justify-content: flex-end; gap: 16px;">
                                        <button class="btn btn-secondary" @click="currentView = 'dashboard'">返回</button>
                                        <button class="btn btn-primary" @click="designStep = 2" :disabled="!selectedTemplate">下一步</button>
                                    </div>
                                </div>

                                <!-- 步骤2：填写信息 -->
                                <div v-else-if="designStep === 2" key="step2">
                                    <h2 style="margin-bottom: 24px;">填写课程信息</h2>
                                    <form @submit.prevent="generateDesign">
                                        <div class="grid grid-2">
                                            <div class="form-group">
                                                <label class="form-label">课程标题 *</label>
                                                <input type="text" class="form-input" v-model="designForm.title" 
                                                       placeholder="例如：秦统一中国" required>
                                            </div>
                                            <div class="form-group">
                                                <label class="form-label">年级班级 *</label>
                                                <input type="text" class="form-input" v-model="designForm.grade" 
                                                       placeholder="例如：七年级（3）班" required>
                                            </div>
                                            <div class="form-group">
                                                <label class="form-label">课时安排</label>
                                                <select class="form-select" v-model="designForm.duration">
                                                    <option value="45">45分钟（1课时）</option>
                                                    <option value="90">90分钟（2课时）</option>
                                                </select>
                                            </div>
                                            <div class="form-group">
                                                <label class="form-label">教材版本</label>
                                                <select class="form-select" v-model="designForm.textbook">
                                                    <option>人教版</option>
                                                    <option>北师大版</option>
                                                    <option>苏教版</option>
                                                </select>
                                            </div>
                                        </div>
                                        <div class="form-group">
                                            <label class="form-label">核心知识点</label>
                                            <textarea class="form-textarea" v-model="designForm.keyPoints" 
                                                      placeholder="请输入本课的核心知识点，用逗号分隔"></textarea>
                                        </div>
                                        <div class="form-group">
                                            <label class="form-label">特殊要求（可选）</label>
                                            <textarea class="form-textarea" v-model="designForm.requirements" 
                                                      placeholder="如有特殊教学要求或需要强调的内容，请在此说明"></textarea>
                                        </div>
                                        <div style="margin-top: 40px; display: flex; justify-content: flex-end; gap: 16px;">
                                            <button type="button" class="btn btn-secondary" @click="designStep = 1">上一步</button>
                                            <button type="submit" class="btn btn-primary">生成教学设计</button>
                                        </div>
                                    </form>
                                </div>

                                <!-- 步骤3：AI生成 -->
                                <div v-else-if="designStep === 3" key="step3">
                                    <div class="loading">
                                        <div class="loading-spinner"></div>
                                        <p class="loading-text">AI正在为您生成教学设计...</p>
                                        <div class="progress-bar" style="width: 300px; margin: 16px auto;">
                                            <div class="progress-fill" :style="{ width: generationProgress + '%' }"></div>
                                        </div>
                                        <p style="font-size: 14px; color: var(--gray-600);">{{ generationStatus }}</p>
                                    </div>
                                </div>

                                <!-- 步骤4：编辑优化 -->
                                <div v-else-if="designStep === 4" key="step4">
                                    <h2 style="margin-bottom: 24px;">编辑教学设计</h2>
                                    
                                    <div class="alert alert-success" style="margin-bottom: 24px;">
                                        <span>✅</span>
                                        <div>
                                            <strong>生成成功！</strong>
                                            <p style="margin: 0;">AI已为您生成教学设计初稿，您可以根据需要进行调整。</p>
                                        </div>
                                    </div>

                                    <div class="editor">
                                        <div class="editor-toolbar">
                                            <button class="editor-btn" @click="formatText('bold')">
                                                <strong>B</strong>
                                            </button>
                                            <button class="editor-btn" @click="formatText('italic')">
                                                <em>I</em>
                                            </button>
                                            <button class="editor-btn" @click="formatText('underline')">
                                                <u>U</u>
                                            </button>
                                            <div style="width: 1px; background: var(--gray-300); margin: 0 8px;"></div>
                                            <button class="editor-btn" @click="aiOptimize">
                                                ✨ AI优化
                                            </button>
                                            <button class="editor-btn" @click="showPPTModal = true">
                                                🎯 生成PPT
                                            </button>
                                        </div>
                                        <div class="editor-content" contenteditable="true" @input="updateContent">
                                            <h3>课题：{{ designForm.title }}</h3>
                                            <p><strong>教学对象：</strong>{{ designForm.grade }}</p>
                                            <p><strong>课时安排：</strong>{{ designForm.duration }}分钟</p>
                                            
                                            <h4>一、教学目标</h4>
                                            <p>1. 知识与技能：了解秦统一六国的历史背景、过程和意义</p>
                                            <p>2. 过程与方法：通过史料分析，培养学生的历史思维能力</p>
                                            <p>3. 情感态度与价值观：认识统一对中华民族发展的重要意义</p>
                                            
                                            <h4>二、教学重难点</h4>
                                            <p><strong>重点：</strong>秦统一的历史条件和过程</p>
                                            <p><strong>难点：</strong>理解秦统一的历史必然性和深远影响</p>
                                            
                                            <h4>三、教学过程</h4>
                                            <p><strong>1. 导入新课（5分钟）</strong></p>
                                            <p>通过展示战国七雄地图，引导学生思考：为什么最终是秦国完成了统一？</p>
                                            
                                            <p><strong>2. 新课讲授（30分钟）</strong></p>
                                            <p>（1）秦统一的历史条件</p>
                                            <ul>
                                                <li>经济基础：商鞅变法后秦国的强大</li>
                                                <li>军事实力：强大的军队和先进的军事制度</li>
                                                <li>地理优势：关中地区的有利地形</li>
                                            </ul>
                                            
                                            <p>（2）统一过程</p>
                                            <ul>
                                                <li>远交近攻的策略</li>
                                                <li>六国的依次灭亡</li>
                                                <li>公元前221年完成统一</li>
                                            </ul>
                                            
                                            <p><strong>3. 课堂小结（10分钟）</strong></p>
                                            <p>总结秦统一的历史意义，强调统一对中华文明发展的重要性。</p>
                                            
                                            <h4>四、作业布置</h4>
                                            <p>1. 完成课后练习题1-3题</p>
                                            <p>2. 思考题：如果你是战国时期的一位谋士，你会给你的君主什么建议？</p>
                                        </div>
                                    </div>

                                    <div style="margin-top: 40px; display: flex; justify-content: space-between;">
                                        <button class="btn btn-secondary" @click="designStep = 2">重新生成</button>
                                        <div style="display: flex; gap: 16px;">
                                            <button class="btn btn-secondary" @click="saveAsDraft">保存草稿</button>
                                            <button class="btn btn-success" @click="saveDesign">
                                                <span>💾</span>
                                                保存到资源库
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </transition>
                        </div>
                    </div>

                    <!-- 资源库 -->
                    <div v-else-if="currentView === 'resources'" key="resources">
                        <h1 style="margin-bottom: 32px;">教学资源库</h1>
                        
                        <!-- 搜索和筛选 -->
                        <div class="card" style="margin-bottom: 24px;">
                            <div style="display: flex; gap: 16px; flex-wrap: wrap;">
                                <input type="text" class="form-input" placeholder="搜索教学资源..." 
                                       style="flex: 1; min-width: 200px;" v-model="searchQuery">
                                <select class="form-select" style="width: 150px;" v-model="filterType">
                                    <option value="">全部类型</option>
                                    <option value="design">教学设计</option>
                                    <option value="ppt">PPT课件</option>
                                    <option value="quiz">试题试卷</option>
                                </select>
                                <select class="form-select" style="width: 150px;" v-model="filterGrade">
                                    <option value="">全部年级</option>
                                    <option value="七年级上">七年级上</option>
                                    <option value="七年级下">七年级下</option>
                                    <option value="八年级上">八年级上</option>
                                    <option value="八年级下">八年级下</option>
                                </select>
                                <button class="btn btn-primary">
                                    <span>🔍</span>
                                    搜索
                                </button>
                            </div>
                        </div>

                        <!-- 资源列表 -->
                        <div class="grid grid-2">
                            <div v-for="resource in filteredResources" :key="resource.id" class="card resource-card">
                                <div class="resource-icon" :style="{ background: getResourceColor(resource.type) }">
                                    {{ resource.icon }}
                                </div>
                                <div class="resource-content">
                                    <h3 class="resource-title">{{ resource.title }}</h3>
                                    <div class="resource-meta">
                                        <span class="tag">{{ resource.grade }}</span>
                                        <span class="tag">{{ resource.type }}</span>
                                        <span class="tag" v-if="resource.keywords" v-for="keyword in resource.keywords.slice(0, 2)">
                                            {{ keyword }}
                                        </span>
                                    </div>
                                    <p class="resource-desc">{{ resource.description }}</p>
                                    <div class="resource-actions">
                                        <span class="resource-date">{{ resource.date }}</span>
                                        <div style="display: flex; gap: 8px;">
                                            <button class="btn btn-secondary btn-sm" @click="previewResource(resource)">预览</button>
                                            <button class="btn btn-primary btn-sm" @click="editResource(resource)">
                                                {{ resource.type === 'PPT课件' ? '下载' : '编辑' }}
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div v-if="filteredResources.length === 0" class="card">
                            <div class="empty-state">
                                <div class="empty-icon">🔍</div>
                                <p>没有找到符合条件的资源</p>
                                <button class="btn btn-primary" style="margin-top: 16px;" @click="resetFilters">
                                    重置筛选条件
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- 个人中心 -->
                    <div v-else-if="currentView === 'profile'" key="profile">
                        <h1 style="margin-bottom: 32px;">个人中心</h1>
                        
                        <div class="grid grid-2">
                            <!-- 个人信息 -->
                            <div class="card">
                                <h2 style="font-size: 20px; margin-bottom: 24px;">个人信息</h2>
                                <form @submit.prevent="updateProfile">
                                    <div class="form-group">
                                        <label class="form-label">姓名</label>
                                        <input type="text" class="form-input" v-model="profile.name">
                                    </div>
                                    <div class="form-group">
                                        <label class="form-label">学校</label>
                                        <input type="text" class="form-input" v-model="profile.school">
                                    </div>
                                    <div class="form-group">
                                        <label class="form-label">任教年级</label>
                                        <select class="form-select" v-model="profile.grade">
                                            <option>七年级</option>
                                            <option>八年级</option>
                                            <option>九年级</option>
                                        </select>
                                    </div>
                                    <button type="submit" class="btn btn-primary">保存修改</button>
                                </form>
                            </div>

                            <!-- 使用统计 -->
                            <div class="card">
                                <h2 style="font-size: 20px; margin-bottom: 24px;">使用统计</h2>
                                <div style="display: grid; gap: 16px;">
                                    <div style="display: flex; justify-content: space-between; padding: 16px; background: var(--gray-50); border-radius: var(--radius-md);">
                                        <span>教学设计</span>
                                        <strong style="color: var(--primary-color);">{{ stats.designs }} 份</strong>
                                    </div>
                                    <div style="display: flex; justify-content: space-between; padding: 16px; background: var(--gray-50); border-radius: var(--radius-md);">
                                        <span>PPT课件</span>
                                        <strong style="color: var(--secondary-color);">{{ stats.ppts }} 份</strong>
                                    </div>
                                    <div style="display: flex; justify-content: space-between; padding: 16px; background: var(--gray-50); border-radius: var(--radius-md);">
                                        <span>试题试卷</span>
                                        <strong style="color: var(--accent-color);">{{ stats.quizzes }} 份</strong>
                                    </div>
                                    <div style="display: flex; justify-content: space-between; padding: 16px; background: var(--gray-50); border-radius: var(--radius-md);">
                                        <span>累计节省时间</span>
                                        <strong>{{ stats.timeSaved }} 小时</strong>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 偏好设置 -->
                        <div class="card" style="margin-top: 24px;">
                            <h2 style="font-size: 20px; margin-bottom: 24px;">偏好设置</h2>
                            <div class="grid grid-2">
                                <div class="form-group">
                                    <label class="form-label">默认教学模板</label>
                                    <select class="form-select" v-model="preferences.defaultTemplate">
                                        <option value="intro">新课导入型</option>
                                        <option value="detail">知识精讲型</option>
                                        <option value="review">复习巩固型</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label class="form-label">默认课时</label>
                                    <select class="form-select" v-model="preferences.defaultDuration">
                                        <option value="45">45分钟</option>
                                        <option value="90">90分钟</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label class="form-label">常用题型</label>
                                    <select class="form-select" v-model="preferences.questionTypes">
                                        <option>选择题为主</option>
                                        <option>综合题型</option>
                                        <option>材料分析题为主</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label class="form-label">PPT风格</label>
                                    <select class="form-select" v-model="preferences.pptStyle">
                                        <option>经典学术风</option>
                                        <option>现代简约风</option>
                                        <option>活泼卡通风</option>
                                    </select>
                                </div>
                            </div>
                            <button class="btn btn-primary" @click="savePreferences">保存偏好设置</button>
                        </div>
                    </div>
                </transition>
            </main>

            <!-- PPT生成模态框 -->
            <div v-if="showPPTModal" class="modal-overlay" @click.self="showPPTModal = false">
                <div class="modal">
                    <div class="modal-header">
                        <h3 class="modal-title">生成PPT课件</h3>
                        <button class="modal-close" @click="showPPTModal = false">&times;</button>
                    </div>
                    <div class="modal-body">
                        <div class="form-group">
                            <label class="form-label">选择PPT模板风格</label>
                            <div class="template-grid" style="grid-template-columns: repeat(2, 1fr);">
                                <div class="template-item" :class="{ selected: pptTemplate === 'academic' }"
                                     @click="pptTemplate = 'academic'">
                                    <div class="template-icon">📚</div>
                                    <div class="template-name">经典学术风</div>
                                </div>
                                <div class="template-item" :class="{ selected: pptTemplate === 'modern' }"
                                     @click="pptTemplate = 'modern'">
                                    <div class="template-icon">🎨</div>
                                    <div class="template-name">现代简约风</div>
                                </div>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="form-label">页面数量</label>
                            <select class="form-select" v-model="pptPages">
                                <option value="auto">自动（根据内容）</option>
                                <option value="10">10-15页</option>
                                <option value="15">15-20页</option>
                                <option value="20">20-25页</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label class="form-label">
                                <input type="checkbox" v-model="includeExercises" style="margin-right: 8px;">
                                包含课堂练习页面
                            </label>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button class="btn btn-secondary" @click="showPPTModal = false">取消</button>
                        <button class="btn btn-primary" @click="generatePPT">
                            <span>🎯</span>
                            生成PPT
                        </button>
                    </div>
                </div>
            </div>

            <!-- 智能组卷模态框 -->
            <div v-if="showQuizModal" class="modal-overlay" @click.self="showQuizModal = false">
                <div class="modal" style="max-width: 900px;">
                    <div class="modal-header">
                        <h3 class="modal-title">
                            <span>🧠</span>
                            智能组卷系统
                        </h3>
                        <button class="modal-close" @click="showQuizModal = false">&times;</button>
                    </div>
                    <div class="modal-body" style="padding: 0;">
                        <!-- 组卷步骤指示器 -->
                        <div class="steps" style="padding: 24px 24px 0;">
                            <div class="step" :class="{ active: quizStep === 1, completed: quizStep > 1 }">
                                <div class="step-number">{{ quizStep > 1 ? '✓' : '1' }}</div>
                                <div class="step-label">基础设置</div>
                            </div>
                            <div class="step" :class="{ active: quizStep === 2, completed: quizStep > 2 }">
                                <div class="step-number">{{ quizStep > 2 ? '✓' : '2' }}</div>
                                <div class="step-label">知识点选择</div>
                            </div>
                            <div class="step" :class="{ active: quizStep === 3, completed: quizStep > 3 }">
                                <div class="step-number">{{ quizStep > 3 ? '✓' : '3' }}</div>
                                <div class="step-label">题型配置</div>
                            </div>
                            <div class="step" :class="{ active: quizStep === 4, completed: quizStep > 4 }">
                                <div class="step-number">{{ quizStep > 4 ? '✓' : '4' }}</div>
                                <div class="step-label">预览确认</div>
                            </div>
                        </div>

                        <div style="padding: 24px;">
                            <transition name="slide" mode="out-in">
                                <!-- 步骤1：基础设置 -->
                                <div v-if="quizStep === 1" key="quiz-step1">
                                    <h4 style="margin-bottom: 20px;">📋 试卷基础信息</h4>
                                    <div class="grid grid-2">
                                        <div class="form-group">
                                            <label class="form-label">试卷名称 *</label>
                                            <input type="text" class="form-input" v-model="quizForm.title" 
                                                   placeholder="例如：第三单元测试卷">
                                        </div>
                                        <div class="form-group">
                                            <label class="form-label">适用年级 *</label>
                                            <select class="form-select" v-model="quizForm.grade">
                                                <option value="七年级上">七年级上册</option>
                                                <option value="七年级下">七年级下册</option>
                                                <option value="八年级上">八年级上册</option>
                                                <option value="八年级下">八年级下册</option>
                                                <option value="九年级上">九年级上册</option>
                                                <option value="九年级下">九年级下册</option>
                                            </select>
                                        </div>
                                        <div class="form-group">
                                            <label class="form-label">考试时长</label>
                                            <select class="form-select" v-model="quizForm.duration">
                                                <option value="45">45分钟</option>
                                                <option value="60">60分钟</option>
                                                <option value="90">90分钟</option>
                                                <option value="120">120分钟</option>
                                            </select>
                                        </div>
                                        <div class="form-group">
                                            <label class="form-label">试卷类型</label>
                                            <select class="form-select" v-model="quizForm.type">
                                                <option value="practice">日常练习</option>
                                                <option value="unit">单元测试</option>
                                                <option value="midterm">期中考试</option>
                                                <option value="final">期末考试</option>
                                                <option value="mock">模拟考试</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <label class="form-label">总分设置</label>
                                        <div style="display: flex; align-items: center; gap: 16px;">
                                            <input type="number" class="form-input" v-model="quizForm.totalScore" 
                                                   style="width: 120px;" min="50" max="150">
                                            <span>分</span>
                                            <div class="alert alert-info" style="margin: 0; padding: 8px 12px; font-size: 14px;">
                                                💡 系统将根据题型自动分配分值
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- 步骤2：知识点选择 -->
                                <div v-else-if="quizStep === 2" key="quiz-step2">
                                    <h4 style="margin-bottom: 20px;">🎯 选择考查知识点</h4>
                                    <div class="form-group">
                                        <label class="form-label">选择单元范围</label>
                                        <select class="form-select" v-model="selectedUnit" @change="loadKnowledgePoints">
                                            <option value="">请选择单元</option>
                                            <option value="unit1">第一单元：中国境内早期人类与文明的起源</option>
                                            <option value="unit2">第二单元：夏商周时期</option>
                                            <option value="unit3">第三单元：秦汉时期</option>
                                            <option value="unit4">第四单元：三国两晋南北朝时期</option>
                                            <option value="unit5">第五单元：隋唐时期</option>
                                            <option value="unit6">第六单元：宋元时期</option>
                                        </select>
                                    </div>
                                    
                                    <div v-if="selectedUnit" class="form-group">
                                        <label class="form-label">
                                            具体知识点 
                                            <span style="font-size: 14px; color: var(--gray-500);">
                                                (已选择 {{ selectedKnowledgePoints.length }} 个)
                                            </span>
                                        </label>
                                        <div style="max-height: 300px; overflow-y: auto; border: 2px solid var(--gray-300); border-radius: var(--radius-md); padding: 16px;">
                                            <div v-for="point in knowledgePoints" :key="point.id" 
                                                 style="margin-bottom: 12px; padding: 12px; border: 1px solid var(--gray-200); border-radius: var(--radius-sm); cursor: pointer;"
                                                 :style="{ 
                                                     'background': selectedKnowledgePoints.includes(point.id) ? 'var(--primary-bg)' : 'white',
                                                     'border-color': selectedKnowledgePoints.includes(point.id) ? 'var(--primary-color)' : 'var(--gray-200)'
                                                 }"
                                                 @click="toggleKnowledgePoint(point.id)">
                                                <div style="display: flex; justify-content: space-between; align-items: start;">
                                                    <div style="flex: 1;">
                                                        <div style="font-weight: 500; margin-bottom: 4px;">
                                                            <input type="checkbox" :checked="selectedKnowledgePoints.includes(point.id)" 
                                                                   style="margin-right: 8px;" @click.stop>
                                                            {{ point.name }}
                                                        </div>
                                                        <div style="font-size: 14px; color: var(--gray-600);">{{ point.description }}</div>
                                                        <div style="margin-top: 8px;">
                                                            <span class="tag" :style="{ background: getDifficultyColor(point.difficulty) }">
                                                                {{ point.difficulty }}
                                                            </span>
                                                            <span class="tag">{{ point.questionCount }} 道题</span>
                                                        </div>
                                                    </div>
                                                    <div style="font-size: 12px; color: var(--gray-500);">
                                                        权重: {{ point.weight }}%
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div v-if="selectedKnowledgePoints.length > 0" class="alert alert-success">
                                        <span>✅</span>
                                        <div>
                                            已选择 {{ selectedKnowledgePoints.length }} 个知识点，
                                            预计可生成 {{ estimatedQuestionCount }} 道题目
                                        </div>
                                    </div>
                                </div>

                                <!-- 步骤3：题型配置 -->
                                <div v-else-if="quizStep === 3" key="quiz-step3">
                                    <h4 style="margin-bottom: 20px;">⚙️ 题型与难度配置</h4>
                                    
                                    <!-- 智能推荐 -->
                                    <div class="alert alert-info" style="margin-bottom: 20px;">
                                        <span>🤖</span>
                                        <div>
                                            <strong>AI智能推荐：</strong>
                                            根据您选择的知识点和试卷类型，推荐以下配置
                                            <button class="btn btn-sm" style="margin-left: 12px; padding: 4px 12px;" 
                                                    @click="applyRecommendedConfig">
                                                采用推荐配置
                                            </button>
                                        </div>
                                    </div>

                                    <div class="grid grid-2">
                                        <!-- 题型配置 -->
                                        <div>
                                            <h5 style="margin-bottom: 16px;">题型分布</h5>
                                            <div style="display: grid; gap: 16px;">
                                                <div v-for="(config, type) in questionTypeConfig" :key="type"
                                                     style="padding: 16px; border: 2px solid var(--gray-300); border-radius: var(--radius-md);">
                                                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 12px;">
                                                        <div>
                                                            <strong>{{ config.name }}</strong>
                                                            <div style="font-size: 14px; color: var(--gray-600);">{{ config.description }}</div>
                                                        </div>
                                                        <label style="display: flex; align-items: center; gap: 8px;">
                                                            <input type="checkbox" v-model="config.enabled" @change="updateQuestionConfig">
                                                            启用
                                                        </label>
                                                    </div>
                                                    <div v-if="config.enabled" style="display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 12px; align-items: center;">
                                                        <div>
                                                            <label style="font-size: 14px; color: var(--gray-600);">题目数量</label>
                                                            <input type="number" class="form-input" v-model="config.count" 
                                                                   :min="config.min" :max="config.max" @input="updateQuestionConfig">
                                                        </div>
                                                        <div>
                                                            <label style="font-size: 14px; color: var(--gray-600);">每题分值</label>
                                                            <input type="number" class="form-input" v-model="config.score" 
                                                                   min="1" max="20" @input="updateQuestionConfig">
                                                        </div>
                                                        <div>
                                                            <label style="font-size: 14px; color: var(--gray-600);">小计</label>
                                                            <div style="font-weight: 600; color: var(--primary-color); padding: 8px;">
                                                                {{ config.count * config.score }} 分
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- 难度配置 -->
                                        <div>
                                            <h5 style="margin-bottom: 16px;">难度分布</h5>
                                            <div style="padding: 20px; border: 2px solid var(--gray-300); border-radius: var(--radius-md);">
                                                <div v-for="(percent, difficulty) in difficultyDistribution" :key="difficulty"
                                                     style="margin-bottom: 16px;">
                                                    <div style="display: flex; justify-content: space-between; margin-bottom: 8px;">
                                                        <span>{{ getDifficultyLabel(difficulty) }}</span>
                                                        <span>{{ percent }}%</span>
                                                    </div>
                                                    <input type="range" v-model="difficultyDistribution[difficulty]" 
                                                           min="0" max="100" step="5" 
                                                           style="width: 100%;" @input="normalizeDifficulty">
                                                    <div class="progress-bar" style="height: 6px; margin-top: 4px;">
                                                        <div class="progress-fill" 
                                                             :style="{ 
                                                                 width: percent + '%',
                                                                 background: getDifficultyColor(difficulty)
                                                             }"></div>
                                                    </div>
                                                </div>
                                                <div style="margin-top: 16px; padding-top: 16px; border-top: 1px solid var(--gray-200);">
                                                    <div style="display: flex; justify-content: space-between; font-weight: 600;">
                                                        <span>总计</span>
                                                        <span>{{ Object.values(difficultyDistribution).reduce((a, b) => a + b, 0) }}%</span>
                                                    </div>
                                                </div>
                                            </div>

                                            <!-- 总分统计 -->
                                            <div style="margin-top: 20px; padding: 16px; background: var(--gray-50); border-radius: var(--radius-md);">
                                                <h6 style="margin-bottom: 12px;">试卷统计</h6>
                                                <div style="display: grid; gap: 8px; font-size: 14px;">
                                                    <div style="display: flex; justify-content: space-between;">
                                                        <span>总题数：</span>
                                                        <span>{{ totalQuestionCount }} 题</span>
                                                    </div>
                                                    <div style="display: flex; justify-content: space-between;">
                                                        <span>总分值：</span>
                                                        <span style="color: var(--primary-color); font-weight: 600;">{{ calculatedTotalScore }} 分</span>
                                                    </div>
                                                    <div style="display: flex; justify-content: space-between;">
                                                        <span>预计用时：</span>
                                                        <span>{{ estimatedTime }} 分钟</span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- 步骤4：预览确认 -->
                                <div v-else-if="quizStep === 4" key="quiz-step4">
                                    <h4 style="margin-bottom: 20px;">👀 试卷预览与确认</h4>
                                    
                                    <!-- 试卷信息摘要 -->
                                    <div style="padding: 20px; background: var(--gray-50); border-radius: var(--radius-md); margin-bottom: 20px;">
                                        <h5 style="margin-bottom: 16px;">{{ quizForm.title }}</h5>
                                        <div class="grid grid-3" style="gap: 16px;">
                                            <div>
                                                <div style="font-size: 14px; color: var(--gray-600);">适用年级</div>
                                                <div style="font-weight: 500;">{{ quizForm.grade }}</div>
                                            </div>
                                            <div>
                                                <div style="font-size: 14px; color: var(--gray-600);">考试时长</div>
                                                <div style="font-weight: 500;">{{ quizForm.duration }} 分钟</div>
                                            </div>
                                            <div>
                                                <div style="font-size: 14px; color: var(--gray-600);">试卷总分</div>
                                                <div style="font-weight: 500; color: var(--primary-color);">{{ calculatedTotalScore }} 分</div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- 题型结构预览 -->
                                    <div style="margin-bottom: 20px;">
                                        <h6 style="margin-bottom: 12px;">题型结构</h6>
                                        <div style="display: grid; gap: 12px;">
                                            <div v-for="(config, type) in enabledQuestionTypes" :key="type"
                                                 style="display: flex; justify-content: space-between; align-items: center; padding: 12px; background: white; border-radius: var(--radius-sm); border: 1px solid var(--gray-200);">
                                                <div>
                                                    <span style="font-weight: 500;">{{ config.name }}</span>
                                                    <span style="margin-left: 12px; font-size: 14px; color: var(--gray-600);">
                                                        {{ config.count }} 题 × {{ config.score }} 分
                                                    </span>
                                                </div>
                                                <div style="font-weight: 600; color: var(--primary-color);">
                                                    {{ config.count * config.score }} 分
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- 知识点覆盖 -->
                                    <div style="margin-bottom: 20px;">
                                        <h6 style="margin-bottom: 12px;">知识点覆盖</h6>
                                        <div style="display: flex; flex-wrap: wrap; gap: 8px;">
                                            <span v-for="pointId in selectedKnowledgePoints" :key="pointId" class="tag">
                                                {{ getKnowledgePointName(pointId) }}
                                            </span>
                                        </div>
                                    </div>

                                    <!-- 生成选项 -->
                                    <div style="padding: 16px; border: 2px solid var(--primary-color); border-radius: var(--radius-md); background: var(--primary-bg);">
                                        <h6 style="margin-bottom: 12px; color: var(--primary-color);">🎯 生成选项</h6>
                                        <div style="display: grid; gap: 12px;">
                                            <label style="display: flex; align-items: center; gap: 8px;">
                                                <input type="checkbox" v-model="generateOptions.includeAnswers">
                                                生成参考答案
                                            </label>
                                            <label style="display: flex; align-items: center; gap: 8px;">
                                                <input type="checkbox" v-model="generateOptions.includeAnalysis">
                                                生成解题分析
                                            </label>
                                            <label style="display: flex; align-items: center; gap: 8px;">
                                                <input type="checkbox" v-model="generateOptions.includeScoring">
                                                生成评分标准
                                            </label>
                                            <label style="display: flex; align-items: center; gap: 8px;">
                                                <input type="checkbox" v-model="generateOptions.multipleVersions">
                                                生成多套试卷（A/B卷）
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </transition>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <div style="display: flex; justify-content: space-between; width: 100%;">
                            <div>
                                <button v-if="quizStep > 1" class="btn btn-secondary" @click="quizStep--">
                                    上一步
                                </button>
                            </div>
                            <div style="display: flex; gap: 12px;">
                                <button class="btn btn-secondary" @click="closeQuizModal">取消</button>
                                <button v-if="quizStep < 4" class="btn btn-primary" @click="nextQuizStep" 
                                        :disabled="!canProceedToNextStep">
                                    下一步
                                </button>
                                <button v-else class="btn btn-success" @click="generateQuiz">
                                    <span>🚀</span>
                                    开始生成试卷
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        const { createApp } = Vue;

        createApp({
            data() {
                return {
                    currentView: 'dashboard',
                    
                    // 教学设计相关
                    designStep: 1,
                    selectedTemplate: '',
                    designForm: {
                        title: '',
                        grade: '',
                        duration: '45',
                        textbook: '人教版',
                        keyPoints: '',
                        requirements: ''
                    },
                    generationProgress: 0,
                    generationStatus: '正在分析您的需求...',
                    
                    // 模板数据
                    templates: [
                        { id: 'intro', icon: '🎯', name: '新课导入型', description: '适合新知识点的讲授，包含情境导入、知识展开、巩固练习等环节' },
                        { id: 'detail', icon: '📚', name: '知识精讲型', description: '深入讲解重点难点，适合专题知识的系统化教学' },
                        { id: 'review', icon: '🔄', name: '复习巩固型', description: '帮助学生梳理知识脉络，强化记忆，适合单元复习' },
                        { id: 'practice', icon: '✏️', name: '练习评讲型', description: '针对习题进行详细讲解，帮助学生掌握解题方法' },
                        { id: 'explore', icon: '🔍', name: '探究活动型', description: '引导学生自主探究，培养历史思维能力' },
                        { id: 'summary', icon: '📋', name: '总结归纳型', description: '系统梳理知识要点，构建知识网络' }
                    ],
                    
                    // 资源数据
                    resources: [
                        {
                            id: 1,
                            title: '第一课：中国境内早期人类的代表——北京人',
                            type: '教学设计',
                            icon: '📄',
                            grade: '七年级上册',
                            keywords: ['北京人', '旧石器时代'],
                            description: '本课重点介绍北京人的发现、生活状况及其在人类进化史上的地位...',
                            date: '3天前'
                        },
                        {
                            id: 2,
                            title: '秦汉时期单元复习课件',
                            type: 'PPT课件',
                            icon: '🎯',
                            grade: '七年级上册',
                            keywords: ['秦朝', '汉朝', '复习'],
                            description: '包含秦汉时期的政治、经济、文化等方面的系统复习内容...',
                            date: '1周前'
                        },
                        {
                            id: 3,
                            title: '第二单元测试卷',
                            type: '试题试卷',
                            icon: '📊',
                            grade: '七年级上册',
                            keywords: ['夏商周', '单元测试'],
                            description: '涵盖夏商周时期的重要知识点，包含选择题、材料分析题等多种题型...',
                            date: '2周前'
                        },
                        {
                            id: 4,
                            title: '汉武帝巩固大一统王朝',
                            type: '教学设计',
                            icon: '📄',
                            grade: '七年级上册',
                            keywords: ['汉武帝', '大一统'],
                            description: '详细讲解汉武帝时期的"推恩令"、"罢黜百家，独尊儒术"等重要政策...',
                            date: '2周前'
                        }
                    ],
                    
                    recentResources: [],
                    
                    // 搜索和筛选
                    searchQuery: '',
                    filterType: '',
                    filterGrade: '',
                    
                    // 模态框
                    showPPTModal: false,
                    showQuizModal: false,
                    pptTemplate: 'academic',
                    pptPages: 'auto',
                    includeExercises: true,
                    quizStep: 1,
                    quizForm: {
                        title: '',
                        grade: '七年级上',
                        duration: '60',
                        type: 'unit',
                        totalScore: 100
                    },
                    selectedUnit: '',
                    selectedKnowledgePoints: [],
                    knowledgePoints: [],
                    questionTypeConfig: {
                        choice: {
                            name: '选择题',
                            description: '单选题和多选题，考查基础知识掌握',
                            enabled: true,
                            count: 20,
                            score: 2,
                            min: 0,
                            max: 30
                        },
                        judge: {
                            name: '判断题',
                            description: '对错判断题，考查概念理解',
                            enabled: true,
                            count: 10,
                            score: 1,
                            min: 0,
                            max: 20
                        },
                        fill: {
                            name: '填空题',
                            description: '填空题，考查知识点记忆',
                            enabled: true,
                            count: 10,
                            score: 2,
                            min: 0,
                            max: 20
                        },
                        analysis: {
                            name: '材料分析题',
                            description: '史料分析题，考查综合运用能力',
                            enabled: true,
                            count: 2,
                            score: 15,
                            min: 0,
                            max: 5
                        },
                        essay: {
                            name: '论述题',
                            description: '开放性论述题，考查深度思考',
                            enabled: false,
                            count: 1,
                            score: 20,
                            min: 0,
                            max: 3
                        }
                    },
                    difficultyDistribution: {
                        easy: 50,
                        medium: 30,
                        hard: 20
                    },
                    generateOptions: {
                        includeAnswers: true,
                        includeAnalysis: true,
                        includeScoring: true,
                        multipleVersions: false
                    },
                    
                    // 知识点数据库
                    knowledgePointsDatabase: {
                        unit1: [
                            {
                                id: 'kp1_1',
                                name: '元谋人',
                                description: '中国境内目前已确认的最早古人类',
                                difficulty: 'easy',
                                weight: 15,
                                questionCount: 8
                            },
                            {
                                id: 'kp1_2',
                                name: '北京人',
                                description: '北京人的发现及其生活特征',
                                difficulty: 'medium',
                                weight: 25,
                                questionCount: 12
                            },
                            {
                                id: 'kp1_3',
                                name: '山顶洞人',
                                description: '山顶洞人的生活方式和社会组织',
                                difficulty: 'medium',
                                weight: 20,
                                questionCount: 10
                            },
                            {
                                id: 'kp1_4',
                                name: '河姆渡文化',
                                description: '长江流域原始农业文明',
                                difficulty: 'hard',
                                weight: 20,
                                questionCount: 9
                            },
                            {
                                id: 'kp1_5',
                                name: '半坡文化',
                                description: '黄河流域原始农业文明',
                                difficulty: 'hard',
                                weight: 20,
                                questionCount: 9
                            }
                        ],
                        unit2: [
                            {
                                id: 'kp2_1',
                                name: '夏朝的建立',
                                description: '中国第一个王朝的建立过程',
                                difficulty: 'medium',
                                weight: 20,
                                questionCount: 10
                            },
                            {
                                id: 'kp2_2',
                                name: '商朝政治制度',
                                description: '商朝的政治制度和社会结构',
                                difficulty: 'hard',
                                weight: 25,
                                questionCount: 12
                            },
                            {
                                id: 'kp2_3',
                                name: '甲骨文',
                                description: '中国最早的成熟文字系统',
                                difficulty: 'medium',
                                weight: 20,
                                questionCount: 11
                            },
                            {
                                id: 'kp2_4',
                                name: '西周分封制',
                                description: '西周的政治制度和社会管理',
                                difficulty: 'hard',
                                weight: 25,
                                questionCount: 13
                            },
                            {
                                id: 'kp2_5',
                                name: '春秋战国',
                                description: '春秋战国时期的政治变革',
                                difficulty: 'hard',
                                weight: 10,
                                questionCount: 8
                            }
                        ],
                        unit3: [
                            {
                                id: 'kp3_1',
                                name: '秦统一六国',
                                description: '秦国统一过程和历史意义',
                                difficulty: 'medium',
                                weight: 25,
                                questionCount: 15
                            },
                            {
                                id: 'kp3_2',
                                name: '秦朝政治制度',
                                description: '中央集权制度的建立',
                                difficulty: 'hard',
                                weight: 30,
                                questionCount: 18
                            },
                            {
                                id: 'kp3_3',
                                name: '汉朝建立',
                                description: '西汉王朝的建立过程',
                                difficulty: 'medium',
                                weight: 20,
                                questionCount: 12
                            },
                            {
                                id: 'kp3_4',
                                name: '汉武帝时期',
                                description: '汉武帝的政治经济文化政策',
                                difficulty: 'hard',
                                weight: 25,
                                questionCount: 16
                            }
                        ]
                    },
                    
                    // 个人信息
                    profile: {
                        name: '张老师',
                        school: '北京市第一中学',
                        grade: '七年级'
                    },
                    
                    // 统计数据
                    stats: {
                        designs: 12,
                        ppts: 8,
                        quizzes: 15,
                        timeSaved: 48
                    },
                    
                    // 偏好设置
                    preferences: {
                        defaultTemplate: 'intro',
                        defaultDuration: '45',
                        questionTypes: '综合题型',
                        pptStyle: '现代简约风'
                    }
                };
            },
            
            computed: {
                filteredResources() {
                    return this.resources.filter(resource => {
                        const matchSearch = !this.searchQuery || 
                            resource.title.includes(this.searchQuery) ||
                            resource.description.includes(this.searchQuery);
                        const matchType = !this.filterType || resource.type.includes(this.filterType);
                        const matchGrade = !this.filterGrade || resource.grade.includes(this.filterGrade);
                        return matchSearch && matchType && matchGrade;
                    });
                },
                estimatedQuestionCount() {
                    return this.selectedKnowledgePoints.length * 3;
                },
                estimatedTime() {
                    return Math.round(this.totalQuestionCount * 1.5);
                },
                calculatedTotalScore() {
                    return Object.values(this.questionTypeConfig)
                        .filter(config => config.enabled)
                        .reduce((total, config) => total + (config.count * config.score), 0);
                },
                totalQuestionCount() {
                    return Object.values(this.questionTypeConfig)
                        .filter(config => config.enabled)
                        .reduce((total, config) => total + config.count, 0);
                },
                enabledQuestionTypes() {
                    return Object.fromEntries(
                        Object.entries(this.questionTypeConfig).filter(([key, config]) => config.enabled)
                    );
                },
                canProceedToNextStep() {
                    switch(this.quizStep) {
                        case 1:
                            return this.quizForm.title && this.quizForm.grade;
                        case 2:
                            return this.selectedUnit && this.selectedKnowledgePoints.length > 0;
                        case 3:
                            return this.totalQuestionCount > 0;
                        case 4:
                            return true;
                        default:
                            return false;
                    }
                }
            },
            
            methods: {
                // 开始教学设计
                startTeachingDesign() {
                    this.currentView = 'teaching-design';
                    this.designStep = 1;
                },
                
                // 生成教学设计
                generateDesign() {
                    this.designStep = 3;
                    this.generationProgress = 0;
                    
                    // 模拟AI生成过程
                    const steps = [
                        { progress: 20, status: '正在分析课程大纲...' },
                        { progress: 40, status: '正在生成教学目标...' },
                        { progress: 60, status: '正在设计教学活动...' },
                        { progress: 80, status: '正在优化教学流程...' },
                        { progress: 100, status: '生成完成！' }
                    ];
                    
                    let currentStep = 0;
                    const interval = setInterval(() => {
                        if (currentStep < steps.length) {
                            this.generationProgress = steps[currentStep].progress;
                            this.generationStatus = steps[currentStep].status;
                            currentStep++;
                        } else {
                            clearInterval(interval);
                            setTimeout(() => {
                                this.designStep = 4;
                            }, 500);
                        }
                    }, 800);
                },
                
                // 保存教学设计
                saveDesign() {
                    const newResource = {
                        id: Date.now(),
                        title: this.designForm.title,
                        type: '教学设计',
                        icon: '📄',
                        grade: '七年级上册',
                        keywords: this.designForm.keyPoints.split(',').map(k => k.trim()),
                        description: `${this.designForm.grade}的教学设计，包含完整的教学目标、教学过程和作业布置`,
                        date: '刚刚'
                    };
                    
                    this.resources.unshift(newResource);
                    this.recentResources.unshift(newResource);
                    this.stats.designs++;
                    
                    alert('教学设计已保存到资源库！');
                    this.currentView = 'resources';
                },
                
                // 保存为草稿
                saveAsDraft() {
                    alert('已保存为草稿！');
                },
                
                // 格式化文本
                formatText(command) {
                    document.execCommand(command, false, null);
                },
                
                // AI优化
                aiOptimize() {
                    alert('AI正在优化您的教学设计，请稍候...');
                },
                
                // 生成PPT
                generatePPT() {
                    this.showPPTModal = false;
                    alert('PPT正在生成中，预计需要30秒...');
                    
                    setTimeout(() => {
                        const newResource = {
                            id: Date.now(),
                            title: this.designForm.title + ' - PPT课件',
                            type: 'PPT课件',
                            icon: '🎯',
                            grade: '七年级上册',
                            keywords: ['PPT', '课件'],
                            description: '基于教学设计自动生成的PPT课件，包含' + (this.includeExercises ? '课堂练习' : '完整内容'),
                            date: '刚刚'
                        };
                        
                        this.resources.unshift(newResource);
                        this.stats.ppts++;
                        alert('PPT生成成功！已保存到资源库。');
                    }, 2000);
                },
                
                // 生成试卷
                generateQuiz() {
                    this.showQuizModal = false;
                    
                    // 显示生成进度
                    const loadingAlert = document.createElement('div');
                    loadingAlert.className = 'alert alert-info';
                    loadingAlert.style.position = 'fixed';
                    loadingAlert.style.top = '20px';
                    loadingAlert.style.right = '20px';
                    loadingAlert.style.zIndex = '9999';
                    loadingAlert.innerHTML = `
                        <div style="display: flex; align-items: center; gap: 12px;">
                            <div class="loading-spinner" style="width: 20px; height: 20px;"></div>
                            <div>
                                <strong>🧠 AI正在智能组卷...</strong>
                                <div style="font-size: 14px; margin-top: 4px;">
                                    正在分析知识点分布和难度配置
                                </div>
                            </div>
                        </div>
                    `;
                    document.body.appendChild(loadingAlert);
                    
                    // 模拟AI生成过程
                    setTimeout(() => {
                        loadingAlert.querySelector('div:last-child div:last-child').textContent = '正在生成题目内容...';
                    }, 1000);
                    
                    setTimeout(() => {
                        loadingAlert.querySelector('div:last-child div:last-child').textContent = '正在优化试卷结构...';
                    }, 2000);
                    
                    setTimeout(() => {
                        loadingAlert.querySelector('div:last-child div:last-child').textContent = '正在生成参考答案...';
                    }, 3000);
                    
                    setTimeout(() => {
                        document.body.removeChild(loadingAlert);
                        
                        // 创建新的试卷资源
                        const newResource = {
                            id: Date.now(),
                            title: this.quizForm.title,
                            type: '试题试卷',
                            icon: '📊',
                            grade: this.quizForm.grade,
                            keywords: ['智能组卷', this.getQuizTypeLabel(this.quizForm.type)],
                            description: this.generateQuizDescription(),
                            date: '刚刚',
                            metadata: {
                                totalScore: this.calculatedTotalScore,
                                questionCount: this.totalQuestionCount,
                                duration: this.quizForm.duration,
                                difficulty: this.getDominantDifficulty(),
                                knowledgePoints: this.selectedKnowledgePoints.length,
                                hasAnswers: this.generateOptions.includeAnswers,
                                hasAnalysis: this.generateOptions.includeAnalysis,
                                multipleVersions: this.generateOptions.multipleVersions
                            }
                        };
                        
                        this.resources.unshift(newResource);
                        this.recentResources.unshift(newResource);
                        this.stats.quizzes++;
                        this.stats.timeSaved += Math.round(this.totalQuestionCount * 0.5); // 每题节省0.5小时
                        
                        // 显示成功消息
                        const successAlert = document.createElement('div');
                        successAlert.className = 'alert alert-success';
                        successAlert.style.position = 'fixed';
                        successAlert.style.top = '20px';
                        successAlert.style.right = '20px';
                        successAlert.style.zIndex = '9999';
                        successAlert.innerHTML = `
                            <div style="display: flex; align-items: center; gap: 12px;">
                                <span style="font-size: 24px;">🎉</span>
                                <div>
                                    <strong>试卷生成成功！</strong>
                                    <div style="font-size: 14px; margin-top: 4px;">
                                        已生成 ${this.totalQuestionCount} 道题目，总分 ${this.calculatedTotalScore} 分
                                        ${this.generateOptions.multipleVersions ? '（包含A/B两套试卷）' : ''}
                                    </div>
                                </div>
                            </div>
                        `;
                        document.body.appendChild(successAlert);
                        
                        setTimeout(() => {
                            document.body.removeChild(successAlert);
                        }, 5000);
                        
                        // 重置表单
                        this.resetQuizForm();
                        
                        // 跳转到资源库
                        this.currentView = 'resources';
                        
                    }, 4000);
                },
                
                // 获取试卷类型标签
                getQuizTypeLabel(type) {
                    const labels = {
                        'practice': '日常练习',
                        'unit': '单元测试',
                        'midterm': '期中考试',
                        'final': '期末考试',
                        'mock': '模拟考试'
                    };
                    return labels[type] || '测试';
                },
                
                // 生成试卷描述
                generateQuizDescription() {
                    const typeLabel = this.getQuizTypeLabel(this.quizForm.type);
                    const dominantDifficulty = this.getDominantDifficulty();
                    const difficultyLabel = this.getDifficultyLabel(dominantDifficulty);
                    
                    return `${typeLabel}试卷，${difficultyLabel}难度为主，涵盖${this.selectedKnowledgePoints.length}个核心知识点，包含${Object.values(this.enabledQuestionTypes).length}种题型，${this.generateOptions.includeAnswers ? '含参考答案' : ''}${this.generateOptions.includeAnalysis ? '和解题分析' : ''}`;
                },
                
                // 获取主要难度
                getDominantDifficulty() {
                    let maxPercent = 0;
                    let dominantDifficulty = 'medium';
                    
                    Object.entries(this.difficultyDistribution).forEach(([difficulty, percent]) => {
                        if (percent > maxPercent) {
                            maxPercent = percent;
                            dominantDifficulty = difficulty;
                        }
                    });
                    
                    return dominantDifficulty;
                },
                
                // 预览资源
                previewResource(resource) {
                    alert(`预览：${resource.title}`);
                },
                
                // 编辑资源
                editResource(resource) {
                    if (resource.type === 'PPT课件') {
                        alert('正在下载PPT文件...');
                    } else {
                        alert(`编辑：${resource.title}`);
                    }
                },
                
                // 获取资源颜色
                getResourceColor(type) {
                    const colors = {
                        '教学设计': 'var(--primary-bg)',
                        'PPT课件': 'var(--secondary-bg)',
                        '试题试卷': '#fef3c7'
                    };
                    return colors[type] || 'var(--gray-100)';
                },
                
                // 重置筛选
                resetFilters() {
                    this.searchQuery = '';
                    this.filterType = '';
                    this.filterGrade = '';
                },
                
                // 更新个人信息
                updateProfile() {
                    alert('个人信息已更新！');
                },
                
                // 保存偏好设置
                savePreferences() {
                    alert('偏好设置已保存！');
                },
                
                // 更新内容
                updateContent(event) {
                    // 实时保存编辑内容
                    console.log('内容已更新');
                },
                
                // 加载知识点
                loadKnowledgePoints() {
                    if (this.selectedUnit && this.knowledgePointsDatabase[this.selectedUnit]) {
                        this.knowledgePoints = this.knowledgePointsDatabase[this.selectedUnit];
                        this.selectedKnowledgePoints = [];
                    }
                },
                
                // 切换知识点
                toggleKnowledgePoint(pointId) {
                    const index = this.selectedKnowledgePoints.indexOf(pointId);
                    if (index > -1) {
                        this.selectedKnowledgePoints.splice(index, 1);
                    } else {
                        this.selectedKnowledgePoints.push(pointId);
                    }
                },
                
                // 获取知识点名称
                getKnowledgePointName(pointId) {
                    for (const unit of Object.values(this.knowledgePointsDatabase)) {
                        const point = unit.find(p => p.id === pointId);
                        if (point) return point.name;
                    }
                    return '未知知识点';
                },
                
                // 获取难度颜色
                getDifficultyColor(difficulty) {
                    const colors = {
                        'easy': '#d1fae5',
                        'medium': '#fef3c7', 
                        'hard': '#fecaca'
                    };
                    return colors[difficulty] || '#f3f4f6';
                },
                
                // 获取难度标签
                getDifficultyLabel(difficulty) {
                    const labels = {
                        'easy': '基础',
                        'medium': '中等',
                        'hard': '困难'
                    };
                    return labels[difficulty] || '未知';
                },
                
                // 下一步
                nextQuizStep() {
                    if (this.canProceedToNextStep && this.quizStep < 4) {
                        this.quizStep++;
                        
                        // 步骤3时应用智能推荐
                        if (this.quizStep === 3) {
                            this.applyRecommendedConfig();
                        }
                    }
                },
                
                // 应用推荐配置
                applyRecommendedConfig() {
                    const typeMap = {
                        'practice': { choice: 15, judge: 10, fill: 8, analysis: 1, essay: 0 },
                        'unit': { choice: 20, judge: 10, fill: 10, analysis: 2, essay: 0 },
                        'midterm': { choice: 25, judge: 15, fill: 12, analysis: 3, essay: 1 },
                        'final': { choice: 30, judge: 20, fill: 15, analysis: 4, essay: 1 },
                        'mock': { choice: 25, judge: 15, fill: 10, analysis: 3, essay: 2 }
                    };
                    
                    const recommended = typeMap[this.quizForm.type] || typeMap['unit'];
                    
                    Object.keys(this.questionTypeConfig).forEach(type => {
                        if (recommended[type] !== undefined) {
                            this.questionTypeConfig[type].count = recommended[type];
                            this.questionTypeConfig[type].enabled = recommended[type] > 0;
                        }
                    });
                    
                    // 根据试卷类型调整难度分布
                    const difficultyMap = {
                        'practice': { easy: 70, medium: 25, hard: 5 },
                        'unit': { easy: 50, medium: 35, hard: 15 },
                        'midterm': { easy: 40, medium: 40, hard: 20 },
                        'final': { easy: 30, medium: 45, hard: 25 },
                        'mock': { easy: 25, medium: 45, hard: 30 }
                    };
                    
                    const recommendedDifficulty = difficultyMap[this.quizForm.type] || difficultyMap['unit'];
                    this.difficultyDistribution = { ...recommendedDifficulty };
                    
                    this.updateQuestionConfig();
                },
                
                // 更新题型配置
                updateQuestionConfig() {
                    // 自动调整分值以接近目标总分
                    const targetScore = this.quizForm.totalScore;
                    const currentScore = this.calculatedTotalScore;
                    
                    if (currentScore !== targetScore && this.totalQuestionCount > 0) {
                        const ratio = targetScore / currentScore;
                        
                        Object.values(this.questionTypeConfig).forEach(config => {
                            if (config.enabled) {
                                config.score = Math.max(1, Math.round(config.score * ratio));
                            }
                        });
                    }
                },
                
                // 标准化难度分布
                normalizeDifficulty() {
                    const total = Object.values(this.difficultyDistribution).reduce((sum, val) => sum + parseInt(val), 0);
                    
                    if (total !== 100) {
                        const factor = 100 / total;
                        Object.keys(this.difficultyDistribution).forEach(key => {
                            this.difficultyDistribution[key] = Math.round(this.difficultyDistribution[key] * factor);
                        });
                        
                        // 确保总和为100
                        const newTotal = Object.values(this.difficultyDistribution).reduce((sum, val) => sum + val, 0);
                        if (newTotal !== 100) {
                            this.difficultyDistribution.easy += (100 - newTotal);
                        }
                    }
                },
                
                // 重置组卷表单
                resetQuizForm() {
                    this.quizStep = 1;
                    this.quizForm = {
                        title: '',
                        grade: '七年级上',
                        duration: '60',
                        type: 'unit',
                        totalScore: 100
                    };
                    this.selectedUnit = '';
                    this.selectedKnowledgePoints = [];
                    this.knowledgePoints = [];
                    
                    // 重置题型配置
                    Object.keys(this.questionTypeConfig).forEach(type => {
                        const config = this.questionTypeConfig[type];
                        config.enabled = ['choice', 'judge', 'fill', 'analysis'].includes(type);
                        config.count = type === 'choice' ? 20 : type === 'judge' ? 10 : type === 'fill' ? 10 : type === 'analysis' ? 2 : 1;
                    });
                    
                    this.difficultyDistribution = { easy: 50, medium: 30, hard: 20 };
                    this.generateOptions = {
                        includeAnswers: true,
                        includeAnalysis: true,
                        includeScoring: true,
                        multipleVersions: false
                    };
                },
                closeQuizModal() {
                    this.showQuizModal = false;
                    this.resetQuizForm();
                }
            },
            
            mounted() {
                // 加载最近使用的资源
                this.recentResources = this.resources.slice(0, 2);
            }
        }).mount('#app');
    </script>
</body>
</html>