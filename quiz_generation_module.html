<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智趣AI - 智能组题系统</title>
    <style>
        :root {
            /* 色彩体系 */
            --primary: #2563eb;
            --primary-dark: #1e40af;
            --primary-light: #3b82f6;
            --primary-bg: #eff6ff;
            --secondary: #06b6d4;
            --secondary-light: #22d3ee;
            --secondary-bg: #ecfeff;
            --accent: #10b981;
            --accent-light: #34d399;
            --accent-bg: #ecfdf5;
            --warning: #f59e0b;
            --warning-light: #fbbf24;
            --warning-bg: #fffbeb;
            --error: #ef4444;
            --error-light: #f87171;
            --error-bg: #fef2f2;
            
            /* 中性色 */
            --gray-900: #111827;
            --gray-800: #1f2937;
            --gray-700: #374151;
            --gray-600: #4b5563;
            --gray-500: #6b7280;
            --gray-400: #9ca3af;
            --gray-300: #d1d5db;
            --gray-200: #e5e7eb;
            --gray-100: #f3f4f6;
            --gray-50: #f9fafb;
            
            /* 间距 */
            --spacing-xs: 4px;
            --spacing-sm: 8px;
            --spacing-md: 16px;
            --spacing-lg: 24px;
            --spacing-xl: 32px;
            
            /* 圆角 */
            --radius-sm: 4px;
            --radius-md: 8px;
            --radius-lg: 12px;
            --radius-xl: 16px;
            
            /* 阴影 */
            --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
            --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
            --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: "PingFang SC", "Helvetica Neue", Arial, sans-serif;
        }
        
        body {
            background-color: var(--gray-50);
            color: var(--gray-900);
            line-height: 1.6;
        }
        
        .modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: rgba(0, 0, 0, 0.5);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1000;
        }
        
        .modal {
            background-color: white;
            border-radius: var(--radius-lg);
            width: 900px;
            max-width: 95%;
            max-height: 90vh;
            overflow-y: auto;
            box-shadow: var(--shadow-xl);
        }
        
        .modal-header {
            padding: var(--spacing-lg);
            border-bottom: 1px solid var(--gray-200);
            display: flex;
            align-items: center;
            justify-content: space-between;
            position: sticky;
            top: 0;
            background: white;
            z-index: 10;
        }
        
        .modal-title {
            font-weight: 600;
            font-size: 20px;
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
            color: var(--gray-900);
        }
        
        .modal-close {
            background: none;
            border: none;
            font-size: 24px;
            cursor: pointer;
            color: var(--gray-500);
            transition: color 0.2s;
        }
        
        .modal-close:hover {
            color: var(--gray-900);
        }
        
        .modal-body {
            padding: 0;
        }
        
        .modal-footer {
            padding: var(--spacing-lg);
            border-top: 1px solid var(--gray-200);
            display: flex;
            justify-content: space-between;
            position: sticky;
            bottom: 0;
            background: white;
            z-index: 10;
        }
        
        /* 步骤指示器 */
        .steps-container {
            padding: var(--spacing-lg) var(--spacing-lg) 0;
        }
        
        .steps {
            display: flex;
            justify-content: space-between;
            margin-bottom: var(--spacing-xl);
            position: relative;
        }
        
        .steps::before {
            content: '';
            position: absolute;
            top: 24px;
            left: 0;
            right: 0;
            height: 3px;
            background: var(--gray-200);
            z-index: 1;
        }
        
        .steps-progress {
            position: absolute;
            top: 24px;
            left: 0;
            height: 3px;
            background: linear-gradient(90deg, var(--primary), var(--accent));
            z-index: 2;
            transition: width 0.5s ease;
        }
        
        .step {
            flex: 1;
            text-align: center;
            position: relative;
            z-index: 3;
        }
        
        .step-number {
            width: 48px;
            height: 48px;
            background: white;
            border: 3px solid var(--gray-300);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 12px;
            font-weight: 700;
            color: var(--gray-500);
            transition: all 0.3s ease;
            font-size: 16px;
        }
        
        .step.active .step-number {
            background: linear-gradient(135deg, var(--primary), var(--accent));
            border-color: var(--primary);
            color: white;
            box-shadow: var(--shadow-lg);
        }
        
        .step.completed .step-number {
            background: var(--accent);
            border-color: var(--accent);
            color: white;
        }
        
        .step-label {
            font-weight: 500;
            color: var(--gray-600);
            transition: color 0.3s ease;
        }
        
        .step.active .step-label {
            color: var(--primary);
            font-weight: 600;
        }
        
        .step.completed .step-label {
            color: var(--accent);
        }
        
        /* 表单样式 */
        .form-container {
            padding: var(--spacing-lg);
        }
        
        .form-group {
            margin-bottom: var(--spacing-lg);
        }
        
        .form-label {
            display: block;
            font-weight: 500;
            margin-bottom: var(--spacing-sm);
            color: var(--gray-700);
            display: flex;
            align-items: center;
            gap: var(--spacing-xs);
        }
        
        .form-input, 
        .form-select {
            width: 100%;
            padding: 12px var(--spacing-md);
            border: 1px solid var(--gray-300);
            border-radius: var(--radius-md);
            font-size: 16px;
            transition: all 0.3s ease;
            color: var(--gray-800);
        }
        
        .form-input:focus, 
        .form-select:focus {
            border-color: var(--primary);
            box-shadow: 0 0 0 3px var(--primary-bg);
            outline: none;
        }
        
        .form-select {
            appearance: none;
            background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%236b7280' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
            background-repeat: no-repeat;
            background-position: right 12px center;
            background-size: 16px;
        }
        
        /* 网格布局 */
        .grid {
            display: grid;
            gap: var(--spacing-lg);
        }
        
        .grid-2 {
            grid-template-columns: repeat(2, 1fr);
        }
        
        .grid-3 {
            grid-template-columns: repeat(3, 1fr);
        }
        
        @media (max-width: 768px) {
            .grid-2, .grid-3 {
                grid-template-columns: 1fr;
            }
        }
        
        /* 按钮 */
        .btn {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: var(--spacing-sm);
            padding: 12px var(--spacing-lg);
            border-radius: var(--radius-md);
            font-weight: 500;
            font-size: 16px;
            cursor: pointer;
            transition: all 0.3s ease;
            border: none;
        }
        
        .btn-primary {
            background: var(--primary);
            color: white;
        }
        
        .btn-primary:hover {
            background: var(--primary-dark);
            box-shadow: var(--shadow-md);
            transform: translateY(-1px);
        }
        
        .btn-secondary {
            background: white;
            color: var(--gray-700);
            border: 1px solid var(--gray-300);
        }
        
        .btn-secondary:hover {
            background: var(--gray-100);
        }
        
        .btn-success {
            background: var(--accent);
            color: white;
        }
        
        .btn-success:hover {
            background: #059669;
        }
        
        .btn-lg {
            padding: 14px var(--spacing-xl);
            font-size: 18px;
        }
        
        /* 知识点选择 */
        .knowledge-container {
            margin-top: var(--spacing-lg);
        }
        
        .knowledge-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: var(--spacing-md);
        }
        
        .knowledge-title {
            font-weight: 600;
            font-size: 18px;
            color: var(--gray-800);
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
        }
        
        .knowledge-search {
            position: relative;
            width: 300px;
        }
        
        .knowledge-search input {
            width: 100%;
            padding: 10px var(--spacing-md);
            padding-left: 36px;
            border: 1px solid var(--gray-300);
            border-radius: var(--radius-md);
        }
        
        .knowledge-search::before {
            content: '🔍';
            position: absolute;
            left: 12px;
            top: 50%;
            transform: translateY(-50%);
            color: var(--gray-500);
        }
        
        .knowledge-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-lg);
        }
        
        .knowledge-item {
            border: 1px solid var(--gray-300);
            border-radius: var(--radius-md);
            padding: var(--spacing-md);
            cursor: pointer;
            transition: all 0.3s ease;
            background: white;
            position: relative;
        }
        
        .knowledge-item:hover {
            border-color: var(--primary-light);
            box-shadow: var(--shadow-md);
            transform: translateY(-2px);
        }
        
        .knowledge-item.selected {
            border-color: var(--primary);
            background-color: var(--primary-bg);
        }
        
        .knowledge-item.selected::before {
            content: '✓';
            position: absolute;
            top: 8px;
            right: 8px;
            width: 20px;
            height: 20px;
            background: var(--primary);
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
        }
        
        .knowledge-name {
            font-weight: 500;
            margin-bottom: 4px;
        }
        
        .knowledge-desc {
            font-size: 14px;
            color: var(--gray-600);
        }
        
        /* 题型配置 */
        .question-type-config {
            background-color: white;
            border-radius: var(--radius-lg);
            padding: var(--spacing-lg);
            margin-bottom: var(--spacing-lg);
            box-shadow: var(--shadow-sm);
        }
        
        .config-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: var(--spacing-lg);
        }
        
        .config-title {
            font-weight: 600;
            font-size: 18px;
            color: var(--gray-800);
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
        }
        
        .config-item {
            display: flex;
            align-items: center;
            padding: var(--spacing-md);
            border: 1px solid var(--gray-200);
            border-radius: var(--radius-md);
            margin-bottom: var(--spacing-md);
            transition: all 0.3s ease;
        }
        
        .config-item:hover {
            border-color: var(--gray-300);
            background-color: var(--gray-50);
        }
        
        .config-item.disabled {
            opacity: 0.6;
        }
        
        .config-icon {
            width: 40px;
            height: 40px;
            border-radius: var(--radius-md);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
            margin-right: var(--spacing-md);
        }
        
        .config-icon.choice {
            background-color: var(--primary-bg);
            color: var(--primary);
        }
        
        .config-icon.fill {
            background-color: var(--secondary-bg);
            color: var(--secondary);
        }
        
        .config-icon.judge {
            background-color: var(--accent-bg);
            color: var(--accent);
        }
        
        .config-icon.analysis {
            background-color: var(--warning-bg);
            color: var(--warning);
        }
        
        .config-info {
            flex: 1;
        }
        
        .config-name {
            font-weight: 500;
            margin-bottom: 4px;
        }
        
        .config-desc {
            font-size: 14px;
            color: var(--gray-600);
        }
        
        .config-controls {
            display: flex;
            align-items: center;
            gap: var(--spacing-md);
        }
        
        .config-toggle {
            position: relative;
            width: 44px;
            height: 24px;
        }
        
        .config-toggle input {
            opacity: 0;
            width: 0;
            height: 0;
        }
        
        .toggle-slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: var(--gray-300);
            transition: .4s;
            border-radius: 34px;
        }
        
        .toggle-slider:before {
            position: absolute;
            content: "";
            height: 18px;
            width: 18px;
            left: 3px;
            bottom: 3px;
            background-color: white;
            transition: .4s;
            border-radius: 50%;
        }
        
        input:checked + .toggle-slider {
            background-color: var(--primary);
        }
        
        input:checked + .toggle-slider:before {
            transform: translateX(20px);
        }
        
        .config-count {
            display: flex;
            align-items: center;
        }
        
        .count-input {
            width: 60px;
            text-align: center;
            padding: 6px;
            border: 1px solid var(--gray-300);
            border-radius: var(--radius-md);
            margin: 0 var(--spacing-xs);
        }
        
        .count-btn {
            width: 28px;
            height: 28px;
            display: flex;
            align-items: center;
            justify-content: center;
            border: 1px solid var(--gray-300);
            background: white;
            border-radius: var(--radius-sm);
            cursor: pointer;
            font-size: 16px;
            transition: all 0.2s;
        }
        
        .count-btn:hover {
            background-color: var(--gray-100);
        }
        
        .config-score {
            display: flex;
            align-items: center;
            gap: var(--spacing-xs);
        }
        
        .score-input {
            width: 50px;
            text-align: center;
            padding: 6px;
            border: 1px solid var(--gray-300);
            border-radius: var(--radius-md);
        }
        
        /* 预览区域 */
        .preview-container {
            background-color: white;
            border-radius: var(--radius-lg);
            padding: var(--spacing-lg);
            margin-top: var(--spacing-lg);
            box-shadow: var(--shadow-sm);
        }
        
        .preview-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: var(--spacing-lg);
            padding-bottom: var(--spacing-md);
            border-bottom: 1px solid var(--gray-200);
        }
        
        .preview-title {
            font-weight: 600;
            font-size: 18px;
            color: var(--gray-800);
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
        }
        
        .preview-actions {
            display: flex;
            gap: var(--spacing-sm);
        }
        
        .preview-question {
            margin-bottom: var(--spacing-xl);
            padding-bottom: var(--spacing-lg);
            border-bottom: 1px dashed var(--gray-200);
        }
        
        .question-header {
            display: flex;
            justify-content: space-between;
            margin-bottom: var(--spacing-md);
        }
        
        .question-number {
            font-weight: 600;
            color: var(--primary);
            display: flex;
            align-items: center;
            gap: var(--spacing-xs);
        }
        
        .question-type {
            font-size: 14px;
            color: var(--gray-600);
            background-color: var(--gray-100);
            padding: 4px 8px;
            border-radius: var(--radius-sm);
        }
        
        .question-content {
            margin-bottom: var(--spacing-md);
            line-height: 1.6;
        }
        
        .question-options {
            display: flex;
            flex-direction: column;
            gap: var(--spacing-sm);
            margin-bottom: var(--spacing-md);
        }
        
        .question-option {
            display: flex;
            gap: var(--spacing-md);
            padding: var(--spacing-sm) var(--spacing-md);
            border-radius: var(--radius-md);
            transition: background-color 0.2s;
        }
        
        .question-option:hover {
            background-color: var(--gray-100);
        }
        
        .option-label {
            font-weight: 500;
            color: var(--primary);
            min-width: 24px;
        }
        
        .question-answer {
            margin-top: var(--spacing-md);
            padding: var(--spacing-md);
            background-color: var(--accent-bg);
            border-radius: var(--radius-md);
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
        }
        
        .answer-label {
            font-weight: 600;
            color: var(--accent);
        }
        
        .question-analysis {
            margin-top: var(--spacing-md);
            padding: var(--spacing-md);
            background-color: var(--gray-50);
            border-radius: var(--radius-md);
            border-left: 3px solid var(--gray-300);
        }
        
        .analysis-label {
            font-weight: 600;
            color: var(--gray-700);
            margin-bottom: var(--spacing-xs);
        }
        
        /* 难度标签 */
        .difficulty-tag {
            display: inline-flex;
            align-items: center;
            gap: 4px;
            padding: 4px 8px;
            border-radius: var(--radius-sm);
            font-size: 12px;
            font-weight: 500;
        }
        
        .difficulty-easy {
            background-color: var(--accent-bg);
            color: var(--accent);
        }
        
        .difficulty-medium {
            background-color: var(--warning-bg);
            color: var(--warning);
        }
        
        .difficulty-hard {
            background-color: var(--error-bg);
            color: var(--error);
        }
        
        /* AI助手面板 */
        .ai-assistant-panel {
            position: fixed;
            bottom: 20px;
            right: 20px;
            width: 320px;
            background: white;
            border-radius: var(--radius-lg);
            box-shadow: var(--shadow-xl);
            z-index: 100;
            overflow: hidden;
            transition: all 0.3s ease;
        }
        
        .ai-assistant-header {
            padding: var(--spacing-md);
            background: linear-gradient(135deg, var(--primary), var(--secondary));
            color: white;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .ai-assistant-title {
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: var(--spacing-xs);
        }
        
        .ai-assistant-body {
            padding: var(--spacing-md);
            max-height: 300px;
            overflow-y: auto;
        }
        
        .ai-message {
            margin-bottom: var(--spacing-md);
            padding: var(--spacing-sm) var(--spacing-md);
            background-color: var(--primary-bg);
            border-radius: var(--radius-md);
            border-top-left-radius: 0;
        }
        
        .ai-suggestion {
            display: flex;
            align-items: center;
            gap: var(--spacing-xs);
            padding: var(--spacing-xs) var(--spacing-sm);
            background-color: var(--gray-100);
            border-radius: var(--radius-sm);
            margin-top: var(--spacing-xs);
            cursor: pointer;
            transition: all 0.2s;
        }
        
        .ai-suggestion:hover {
            background-color: var(--primary-bg);
        }
        
        .ai-assistant-input {
            display: flex;
            padding: var(--spacing-sm);
            border-top: 1px solid var(--gray-200);
        }
        
        .ai-input {
            flex: 1;
            border: 1px solid var(--gray-300);
            border-radius: var(--radius-md);
            padding: 8px 12px;
            font-size: 14px;
        }
        
        .ai-send-btn {
            background: var(--primary);
            color: white;
            border: none;
            border-radius: var(--radius-md);
            padding: 8px 12px;
            margin-left: var(--spacing-xs);
            cursor: pointer;
        }
        
        /* 新增：题目标签系统 */
        .tags-container {
            margin-top: var(--spacing-md);
        }
        
        .tags-title {
            font-weight: 500;
            margin-bottom: var(--spacing-sm);
            color: var(--gray-700);
        }
        
        .tags-list {
            display: flex;
            flex-wrap: wrap;
            gap: var(--spacing-xs);
        }
        
        .tag {
            display: inline-flex;
            align-items: center;
            padding: 6px 10px;
            background-color: var(--gray-100);
            border-radius: var(--radius-md);
            font-size: 14px;
            color: var(--gray-700);
            cursor: pointer;
            transition: all 0.2s;
        }
        
        .tag:hover {
            background-color: var(--gray-200);
        }
        
        .tag.selected {
            background-color: var(--primary-bg);
            color: var(--primary);
        }
        
        .tag-add {
            display: inline-flex;
            align-items: center;
            gap: 4px;
            padding: 6px 10px;
            background-color: white;
            border: 1px dashed var(--gray-300);
            border-radius: var(--radius-md);
            font-size: 14px;
            color: var(--gray-500);
            cursor: pointer;
        }
        
        /* 新增：批量操作工具栏 */
        .batch-toolbar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: var(--spacing-md);
            background-color: var(--gray-50);
            border-radius: var(--radius-md);
            margin-bottom: var(--spacing-lg);
        }
        
        .batch-info {
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
            color: var(--gray-700);
        }
        
        .batch-actions {
            display: flex;
            gap: var(--spacing-sm);
        }
        
        /* 新增：试卷设置面板 */
        .paper-settings {
            background-color: white;
            border-radius: var(--radius-lg);
            padding: var(--spacing-lg);
            margin-bottom: var(--spacing-lg);
            box-shadow: var(--shadow-sm);
        }
        
        .settings-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: var(--spacing-lg);
        }
        
        .settings-title {
            font-weight: 600;
            font-size: 18px;
            color: var(--gray-800);
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
        }
        
        .settings-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: var(--spacing-lg);
        }
        
        @media (max-width: 768px) {
            .settings-grid {
                grid-template-columns: 1fr;
            }
        }
        
        .settings-item {
            display: flex;
            flex-direction: column;
            gap: var(--spacing-xs);
        }
        
        .settings-label {
            font-weight: 500;
            color: var(--gray-700);
        }
        
        /* 新增：导出选项 */
        .export-options {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: var(--spacing-md);
            margin-top: var(--spacing-lg);
        }
        
        .export-option {
            border: 1px solid var(--gray-200);
            border-radius: var(--radius-md);
            padding: var(--spacing-md);
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .export-option:hover {
            border-color: var(--primary);
            background-color: var(--primary-bg);
            transform: translateY(-2px);
        }
        
        .export-icon {
            font-size: 24px;
            margin-bottom: var(--spacing-sm);
        }
        
        .export-title {
            font-weight: 500;
            margin-bottom: 4px;
        }
        
        .export-desc {
            font-size: 12px;
            color: var(--gray-600);
        }
    </style>
</head>
<body>
    <div class="modal-overlay" style="display: flex;">
        <div class="modal">
            <div class="modal-header">
                <div class="modal-title">
                    <span>📝</span> 智能组题
                </div>
                <button class="modal-close">&times;</button>
            </div>
            
            <div class="modal-body">
                <div class="steps-container">
                    <div class="steps">
                        <div class="steps-progress" style="width: 50%;"></div>
                        <div class="step completed">
                            <div class="step-number">✓</div>
                            <div class="step-label">选择知识点</div>
                        </div>
                        <div class="step active">
                            <div class="step-number">2</div>
                            <div class="step-label">题型配置</div>
                        </div>
                        <div class="step">
                            <div class="step-number">3</div>
                            <div class="step-label">预览与导出</div>
                        </div>
                    </div>
                </div>
                
                <!-- 步骤2：题型配置 -->
                <div class="form-container">
                    <!-- 试卷基本设置 -->
                    <div class="paper-settings">
                        <div class="settings-header">
                            <div class="settings-title">
                                <span>📋</span> 试卷设置
                            </div>
                        </div>
                        
                        <div class="settings-grid">
                            <div class="settings-item">
                                <div class="settings-label">试卷标题</div>
                                <input type="text" class="form-input" value="明朝的建立 - 单元测试" placeholder="输入试卷标题">
                            </div>
                            <div class="settings-item">
                                <div class="settings-label">总分值</div>
                                <input type="number" class="form-input" value="100" min="0" max="150">
                            </div>
                            <div class="settings-item">
                                <div class="settings-label">难度系数</div>
                                <select class="form-select">
                                    <option>简单</option>
                                    <option selected>中等</option>
                                    <option>困难</option>
                                </select>
                            </div>
                            <div class="settings-item">
                                <div class="settings-label">考试时长（分钟）</div>
                                <input type="number" class="form-input" value="40" min="0">
                            </div>
                            <div class="settings-item">
                                <div class="settings-label">适用年级</div>
                                <select class="form-select">
                                    <option>七年级上册</option>
                                    <option selected>七年级下册</option>
                                    <option>八年级上册</option>
                                    <option>八年级下册</option>
                                    <option>九年级上册</option>
                                    <option>九年级下册</option>
                                </select>
                            </div>
                            <div class="settings-item">
                                <div class="settings-label">试卷类型</div>
                                <select class="form-select">
                                    <option>单元测试</option>
                                    <option>期中考试</option>
                                    <option>期末考试</option>
                                    <option>模拟考试</option>
                                    <option>随堂练习</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 题型配置 -->
                    <div class="question-type-config">
                        <div class="config-header">
                            <div class="config-title">
                                <span>📊</span> 题型配置
                            </div>
                            <div>
                                <button class="btn btn-secondary">重置</button>
                                <button class="btn btn-primary">智能推荐</button>
                            </div>
                        </div>
                        
                        <!-- 选择题配置 -->
                        <div class="config-item">
                            <div class="config-icon choice">A</div>
                            <div class="config-info">
                                <div class="config-name">选择题</div>
                                <div class="config-desc">单选题，每题提供4个选项</div>
                                <div class="tags-container">
                                    <div class="tags-list">
                                        <div class="tag selected">基础知识</div>
                                        <div class="tag selected">历史事件</div>
                                        <div class="tag">历史人物</div>
                                        <div class="tag-add">+ 添加标签</div>
                                    </div>
                                </div>
                            </div>
                            <div class="config-controls">
                                <label class="config-toggle">
                                    <input type="checkbox" checked>
                                    <span class="toggle-slider"></span>
                                </label>
                                <div class="config-count">
                                    <button class="count-btn">-</button>
                                    <input type="number" class="count-input" value="10" min="0" max="20">
                                    <button class="count-btn">+</button>
                                </div>
                                <div class="config-score">
                                    <span>每题</span>
                                    <input type="number" class="score-input" value="3" min="0">
                                    <span>分</span>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 填空题配置 -->
                        <div class="config-item">
                            <div class="config-icon fill">□</div>
                            <div class="config-info">
                                <div class="config-name">填空题</div>
                                <div class="config-desc">考察关键概念、年代、人物等</div>
                                <div class="tags-container">
                                    <div class="tags-list">
                                        <div class="tag selected">重要年代</div>
                                        <div class="tag selected">历史事件</div>
                                        <div class="tag-add">+ 添加标签</div>
                                    </div>
                                </div>
                            </div>
                            <div class="config-controls">
                                <label class="config-toggle">
                                    <input type="checkbox" checked>
                                    <span class="toggle-slider"></span>
                                </label>
                                <div class="config-count">
                                    <button class="count-btn">-</button>
                                    <input type="number" class="count-input" value="5" min="0" max="20">
                                    <button class="count-btn">+</button>
                                </div>
                                <div class="config-score">
                                    <span>每题</span>
                                    <input type="number" class="score-input" value="2" min="0">
                                    <span>分</span>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 判断题配置 -->
                        <div class="config-item">
                            <div class="config-icon judge">✓</div>
                            <div class="config-info">
                                <div class="config-name">判断题</div>
                                <div class="config-desc">判断历史事件或观点的正误</div>
                                <div class="tags-container">
                                    <div class="tags-list">
                                        <div class="tag selected">史实判断</div>
                                        <div class="tag">历史评价</div>
                                        <div class="tag-add">+ 添加标签</div>
                                    </div>
                                </div>
                            </div>
                            <div class="config-controls">
                                <label class="config-toggle">
                                    <input type="checkbox" checked>
                                    <span class="toggle-slider"></span>
                                </label>
                                <div class="config-count">
                                    <button class="count-btn">-</button>
                                    <input type="number" class="count-input" value="5" min="0" max="20">
                                    <button class="count-btn">+</button>
                                </div>
                                <div class="config-score">
                                    <span>每题</span>
                                    <input type="number" class="score-input" value="2" min="0">
                                    <span>分</span>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 分析题配置 -->
                        <div class="config-item">
                            <div class="config-icon analysis">!?</div>
                            <div class="config-info">
                                <div class="config-name">分析题</div>
                                <div class="config-desc">考察对历史事件的深入理解</div>
                                <div class="tags-container">
                                    <div class="tags-list">
                                        <div class="tag selected">历史分析</div>
                                        <div class="tag">历史评价</div>
                                        <div class="tag-add">+ 添加标签</div>
                                    </div>
                                </div>
                            </div>
                            <div class="config-controls">
                                <label class="config-toggle">
                                    <input type="checkbox" checked>
                                    <span class="toggle-slider"></span>
                                </label>
                                <div class="config-count">
                                    <button class="count-btn">-</button>
                                    <input type="number" class="count-input" value="3" min="0" max="10">
                                    <button class="count-btn">+</button>
                                </div>
                                <div class="config-score">
                                    <span>每题</span>
                                    <input type="number" class="score-input" value="5" min="0">
                                    <span>分</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
