<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智趣AI - 智能组题系统</title>
    <style>
        :root {
            /* 颜色变量 */
            --primary: #4361ee;
            --primary-dark: #3a56d4;
            --primary-light: #eef2ff;
            --primary-bg: #f0f5ff;
            --secondary: #7209b7;
            --secondary-dark: #6008a0;
            --secondary-light: #f3e8fa;
            --secondary-bg: #f9f0ff;
            --accent: #4cc9f0;
            --accent-dark: #0bb5e5;
            --accent-light: #e3f8ff;
            --accent-bg: #f0fbff;
            --success: #2ecc71;
            --success-dark: #27ae60;
            --success-light: #e8f8f0;
            --warning: #f39c12;
            --warning-dark: #e67e22;
            --warning-light: #fef8e8;
            --error: #e74c3c;
            --error-dark: #c0392b;
            --error-light: #fdeeec;
            --gray-50: #f9fafb;
            --gray-100: #f3f4f6;
            --gray-200: #e5e7eb;
            --gray-300: #d1d5db;
            --gray-400: #9ca3af;
            --gray-500: #6b7280;
            --gray-600: #4b5563;
            --gray-700: #374151;
            --gray-800: #1f2937;
            --gray-900: #111827;
            --white: #ffffff;
            --black: #000000;
            
            /* 主题色RGB值（用于透明度） */
            --primary-rgb: 67, 97, 238;
            --secondary-rgb: 114, 9, 183;
            --accent-rgb: 76, 201, 240;
            --success-rgb: 46, 204, 113;
            --warning-rgb: 243, 156, 18;
            --error-rgb: 231, 76, 60;
            
            /* 间距 */
            --spacing-xs: 4px;
            --spacing-sm: 8px;
            --spacing-md: 16px;
            --spacing-lg: 24px;
            --spacing-xl: 32px;
            --spacing-2xl: 48px;
            
            /* 圆角 */
            --radius-sm: 4px;
            --radius-md: 8px;
            --radius-lg: 12px;
            --radius-xl: 16px;
            --radius-2xl: 24px;
            --radius-full: 9999px;
            
            /* 阴影 */
            --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
            --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
            --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
            
            /* 字体 */
            --font-sans: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
        }
        
        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }
        
        body {
            font-family: var(--font-sans);
            line-height: 1.5;
            color: var(--gray-800);
            background-color: var(--gray-100);
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: var(--spacing-lg);
        }
        
        /* 模态框样式 */
        .modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: rgba(0, 0, 0, 0.5);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1000;
        }
        
        .modal {
            background-color: white;
            border-radius: var(--radius-lg);
            box-shadow: var(--shadow-xl);
            width: 90%;
            max-width: 900px;
            max-height: 90vh;
            overflow-y: auto;
            position: relative;
        }
        
        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: var(--spacing-lg);
            border-bottom: 1px solid var(--gray-200);
        }
        
        .modal-title {
            font-size: 20px;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
        }
        
        .modal-close {
            background: none;
            border: none;
            font-size: 24px;
            cursor: pointer;
            color: var(--gray-500);
        }
        
        .modal-body {
            padding: var(--spacing-lg);
        }
        
        .modal-footer {
            display: flex;
            justify-content: flex-end;
            gap: var(--spacing-md);
            padding: var(--spacing-lg);
            border-top: 1px solid var(--gray-200);
        }
        
        /* 按钮样式 */
        .btn {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: var(--spacing-sm);
            padding: var(--spacing-sm) var(--spacing-lg);
            border-radius: var(--radius-md);
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
            border: none;
        }
        
        .btn-sm {
            padding: var(--spacing-xs) var(--spacing-sm);
            font-size: 14px;
        }
        
        .btn-primary {
            background-color: var(--primary);
            color: white;
        }
        
        .btn-primary:hover {
            background-color: var(--primary-dark);
        }
        
        .btn-primary:disabled {
            background-color: var(--gray-400);
            cursor: not-allowed;
        }
        
        .btn-secondary {
            background-color: var(--gray-200);
            color: var(--gray-700);
        }
        
        .btn-secondary:hover {
            background-color: var(--gray-300);
        }
        
        .btn-secondary:disabled {
            background-color: var(--gray-200);
            color: var(--gray-400);
            cursor: not-allowed;
        }
        
        .btn-outline {
            background-color: transparent;
            border: 1px solid var(--primary);
            color: var(--primary);
        }
        
        .btn-outline:hover {
            background-color: var(--primary-bg);
        }
        
        /* 表单样式 */
        .form-group {
            margin-bottom: var(--spacing-lg);
        }
        
        .form-label {
            display: block;
            margin-bottom: var(--spacing-sm);
            font-weight: 500;
            color: var(--gray-700);
        }
        
        .form-input, .form-select {
            width: 100%;
            padding: var(--spacing-md);
            border: 1px solid var(--gray-300);
            border-radius: var(--radius-md);
            font-size: 16px;
            transition: border-color 0.2s ease;
        }
        
        .form-input:focus, .form-select:focus {
            outline: none;
            border-color: var(--primary);
            box-shadow: 0 0 0 3px rgba(var(--primary-rgb), 0.1);
        }
        
        /* 网格布局 */
        .grid {
            display: grid;
            gap: var(--spacing-lg);
        }
        
        .grid-2 {
            grid-template-columns: repeat(2, 1fr);
        }
        
        .grid-3 {
            grid-template-columns: repeat(3, 1fr);
        }
        
        /* 步骤指示器 */
        .steps {
            display: flex;
            justify-content: space-between;
            margin-bottom: var(--spacing-xl);
            position: relative;
        }
        
        .steps::before {
            content: '';
            position: absolute;
            top: 24px;
            left: 0;
            right: 0;
            height: 3px;
            background: var(--gray-300);
            z-index: 1;
        }
        
        .steps-progress {
            position: absolute;
            top: 24px;
            left: 0;
            height: 3px;
            background: linear-gradient(90deg, var(--primary), var(--accent));
            z-index: 2;
            transition: width 0.5s ease;
        }
        
        .step {
            flex: 1;
            text-align: center;
            position: relative;
            z-index: 3;
        }
        
        .step-number {
            width: 48px;
            height: 48px;
            background: var(--white);
            border: 3px solid var(--gray-300);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 12px;
            font-weight: 700;
            color: var(--gray-500);
            transition: all 0.3s ease;
            font-size: 16px;
        }
        
        .step.active .step-number,
        .step.completed .step-number {
            background: linear-gradient(135deg, var(--primary), var(--accent));
            border-color: var(--primary);
            color: var(--white);
            box-shadow: var(--shadow-lg);
        }
        
        .step-label {
            font-size: 14px;
            font-weight: 500;
            color: var(--gray-600);
        }
        
        .step.active .step-label,
        .step.completed .step-label {
            color: var(--primary);
            font-weight: 600;
        }
        
        /* 警告框 */
        .alert {
            display: flex;
            align-items: flex-start;
            gap: var(--spacing-md);
            padding: var(--spacing-md);
            border-radius: var(--radius-md);
            margin-bottom: var(--spacing-lg);
        }
        
        .alert-info {
            background-color: var(--primary-bg);
            color: var(--primary-dark);
        }
        
        .alert-success {
            background-color: var(--success-light);
            color: var(--success-dark);
        }
        
        .alert-warning {
            background-color: var(--warning-light);
            color: var(--warning-dark);
        }
        
        /* 知识点选择 */
        .knowledge-points {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-lg);
        }
        
        .knowledge-point {
            background-color: var(--white);
            border: 1px solid var(--gray-300);
            border-radius: var(--radius-md);
            padding: var(--spacing-md);
            cursor: pointer;
            transition: all 0.2s ease;
        }
        
        .knowledge-point:hover {
            border-color: var(--primary);
            box-shadow: 0 0 0 1px var(--primary);
        }
        
        .knowledge-point.selected {
            background-color: var(--primary-bg);
            border-color: var(--primary);
        }
        
        .knowledge-point-title {
            font-weight: 500;
            margin-bottom: var(--spacing-xs);
        }
        
        .knowledge-point-desc {
            font-size: 14px;
            color: var(--gray-600);
        }
        
        /* 题型配置 */
        .question-type {
            background-color: var(--white);
            border-radius: var(--radius-lg);
            padding: var(--spacing-lg);
            margin-bottom: var(--spacing-lg);
            box-shadow: var(--shadow-sm);
        }
        
        .question-type-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: var(--spacing-md);
        }
        
        .question-type-title {
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
            font-weight: 600;
        }
        
        .question-type-desc {
            font-size: 14px;
            color: var(--gray-600);
            margin-bottom: var(--spacing-md);
        }
        
        .question-type-controls {
            display: flex;
            align-items: center;
            gap: var(--spacing-md);
        }
        
        .counter {
            display: flex;
            align-items: center;
        }
        
        .counter-btn {
            width: 32px;
            height: 32px;
            display: flex;
            align-items: center;
            justify-content: center;
            background-color: var(--gray-200);
            border: none;
            border-radius: var(--radius-md);
            cursor: pointer;
            font-size: 18px;
            font-weight: 600;
        }
        
        .counter-btn:hover {
            background-color: var(--gray-300);
        }
        
        .counter-input {
            width: 60px;
            text-align: center;
            border: 1px solid var(--gray-300);
            border-radius: var(--radius-md);
            padding: var(--spacing-xs);
            margin: 0 var(--spacing-sm);
        }
        
        .switch {
            position: relative;
            display: inline-block;
            width: 50px;
            height: 24px;
        }
        
        .switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }
        
        .slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: var(--gray-300);
            transition: .4s;
            border-radius: 34px;
        }
        
        .slider:before {
            position: absolute;
            content: "";
            height: 16px;
            width: 16px;
            left: 4px;
            bottom: 4px;
            background-color: white;
            transition: .4s;
            border-radius: 50%;
        }
        
        input:checked + .slider {
            background-color: var(--primary);
        }
        
        input:checked + .slider:before {
            transform: translateX(26px);
        }
        
        /* 难度分布 */
        .difficulty-distribution {
            margin-bottom: var(--spacing-lg);
        }
        
        .difficulty-slider {
            display: flex;
            align-items: center;
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-md);
        }
        
        .difficulty-label {
            width: 80px;
            font-weight: 500;
        }
        
        .difficulty-bar {
            flex: 1;
            height: 8px;
            background-color: var(--gray-200);
            border-radius: var(--radius-full);
            position: relative;
            overflow: hidden;
        }
        
        .difficulty-fill {
            height: 100%;
            border-radius: var(--radius-full);
            transition: width 0.3s ease;
        }
        
        .difficulty-easy {
            background-color: var(--success);
        }
        
        .difficulty-medium {
            background-color: var(--warning);
        }
        
        .difficulty-hard {
            background-color: var(--error);
        }
        
        .difficulty-value {
            width: 40px;
            text-align: right;
            font-weight: 500;
        }
        
        /* 标签 */
        .tags {
            display: flex;
            flex-wrap: wrap;
            gap: var(--spacing-sm);
            margin-bottom: var(--spacing-md);
        }
        
        .tag {
            display: inline-flex;
            align-items: center;
            gap: var(--spacing-xs);
            padding: var(--spacing-xs) var(--spacing-sm);
            background-color: var(--gray-100);
            border-radius: var(--radius-full);
            font-size: 14px;
            color: var(--gray-700);
        }
        
        .tag-remove {
            cursor: pointer;
            font-weight: 700;
            color: var(--gray-500);
        }
        
        .tag-remove:hover {
            color: var(--error);
        }
        
        /* 预览与导出页面样式 */
        .batch-toolbar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            background-color: var(--gray-50);
            padding: var(--spacing-md);
            border-radius: var(--radius-md);
            margin-bottom: var(--spacing-lg);
        }
        
        .batch-info {
            display: flex;
            align-items: center;
            gap: var(--spacing-md);
        }
        
        .batch-actions {
            display: flex;
            gap: var(--spacing-sm);
        }
        
        .preview-container {
            background-color: var(--white);
            border-radius: var(--radius-lg);
            padding: var(--spacing-xl);
            box-shadow: var(--shadow-md);
            margin-bottom: var(--spacing-lg);
        }
        
        .preview-header {
            text-align: center;
            margin-bottom: var(--spacing-xl);
        }
        
        .preview-title {
            font-size: 24px;
            font-weight: 700;
            margin-bottom: var(--spacing-md);
        }
        
        .preview-meta {
            display: flex;
            justify-content: center;
            gap: var(--spacing-lg);
            color: var(--gray-600);
            font-size: 14px;
        }
        
        .preview-section {
            margin-bottom: var(--spacing-xl);
        }
        
        .preview-section-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: var(--spacing-md);
            padding-bottom: var(--spacing-xs);
            border-bottom: 2px solid var(--primary-light);
        }
        
        .question-list {
            counter-reset: question;
        }
        
        .question-item {
            margin-bottom: var(--spacing-lg);
            counter-increment: question;
        }
        
        .question-item::before {
            content: counter(question) ". ";
            font-weight: 600;
        }
        
        .question-content {
            margin-bottom: var(--spacing-md);
        }
        
        .options-list {
            list-style-type: none;
            padding-left: var(--spacing-xl);
        }
        
        .option-item {
            margin-bottom: var(--spacing-sm);
            display: flex;
            align-items: flex-start;
        }
        
        .option-label {
            font-weight: 500;
            margin-right: var(--spacing-sm);
        }
        
        .answer-section {
            margin-top: var(--spacing-xl);
            padding-top: var(--spacing-lg);
            border-top: 1px dashed var(--gray-300);
        }
        
        .export-options {
            display: flex;
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-lg);
        }
        
        .export-option {
            flex: 1;
            background-color: var(--white);
            border-radius: var(--radius-lg);
            padding: var(--spacing-lg);
            border: 1px solid var(--gray-200);
            cursor: pointer;
            transition: all 0.2s ease;
            text-align: center;
        }
        
        .export-option:hover {
            border-color: var(--primary);
            box-shadow: var(--shadow-md);
        }
        
        .export-option.selected {
            border-color: var(--primary);
            background-color: var(--primary-bg);
        }
        
        .export-icon {
            font-size: 32px;
            margin-bottom: var(--spacing-sm);
            color: var(--primary);
        }
        
        .export-title {
            font-weight: 600;
            margin-bottom: var(--spacing-xs);
        }
        
        .export-desc {
            font-size: 14px;
            color: var(--gray-600);
        }
        
        /* 知识点标签 */
        .knowledge-tags {
            display: flex;
            flex-wrap: wrap;
            gap: var(--spacing-sm);
            margin: var(--spacing-md) 0;
        }
        
        .knowledge-tag {
            background-color: var(--primary-light);
            color: var(--primary-dark);
            padding: var(--spacing-xs) var(--spacing-sm);
            border-radius: var(--radius-full);
            font-size: 14px;
        }
        
        /* 响应式调整 */
        @media (max-width: 768px) {
            .grid-2, .grid-3 {
                grid-template-columns: 1fr;
            }
            
            .steps {
                flex-direction: column;
                gap: var(--spacing-lg);
            }
            
            .steps::before {
                display: none;
            }
            
            .steps-progress {
                display: none;
            }
            
            .step {
                display: flex;
                align-items: center;
                gap: var(--spacing-md);
            }
            
            .step-number {
                margin: 0;
            }
            
            .modal {
                width: 95%;
                max-height: 95vh;
            }
        }
    </style>
</head>
<body>
    <div class="modal-overlay">
        <div class="modal">
            <div class="modal-header">
                <div class="modal-title">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
                        <polyline points="14 2 14 8 20 8"></polyline>
                        <line x1="16" y1="13" x2="8" y2="13"></line>
                        <line x1="16" y1="17" x2="8" y2="17"></line>
                        <polyline points="10 9 9 9 8 9"></polyline>
                    </svg>
                    智能组题
                </div>
                <button class="modal-close">&times;</button>
            </div>
            <div class="modal-body">
                <!-- 步骤指示器 -->
                <div class="steps">
                    <div class="steps-progress" style="width: 0%"></div>
                    <div class="step active" data-step="1">
                        <div class="step-number">1</div>
                        <div class="step-label">选择知识点</div>
                    </div>
                    <div class="step" data-step="2">
                        <div class="step-number">2</div>
                        <div class="step-label">题型配置</div>
                    </div>
                    <div class="step" data-step="3">
                        <div class="step-number">3</div>
                        <div class="step-label">预览与导出</div>
                    </div>
                </div>
                
                <!-- 步骤1：选择知识点 -->
                <div class="step-content" id="step1Content">
                    <div class="form-group">
                        <label class="form-label">选择年级与册次</label>
                        <div class="grid grid-2">
                            <select class="form-select" id="gradeSelect">
                                <option value="7">七年级</option>
                                <option value="8">八年级</option>
                                <option value="9">九年级</option>
                            </select>
                            <select class="form-select" id="volumeSelect">
                                <option value="1">上册</option>
                                <option value="2">下册</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">选择单元</label>
                        <select class="form-select" id="unitSelect">
                            <option value="1">第一单元：中华文明的起源</option>
                            <option value="2">第二单元：夏商周时期</option>
                            <option value="3">第三单元：秦汉统一国家的建立</option>
                            <option value="4">第四单元：三国两晋南北朝的分裂与民族融合</option>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">选择知识点（可多选）</label>
                        <div class="alert alert-info">
                            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <circle cx="12" cy="12" r="10"></circle>
                                <line x1="12" y1="16" x2="12" y2="12"></line>
                                <line x1="12" y1="8" x2="12.01" y2="8"></line>
                            </svg>
                            <div>
                                请选择需要考察的知识点，系统将根据所选知识点智能生成试题。建议选择3-5个相关知识点。
                            </div>
                        </div>
                        
                        <div class="knowledge-points">
                            <div class="knowledge-point" data-id="1">
                                <div class="knowledge-point-title">中华文明的起源</div>
                                <div class="knowledge-point-desc">探索中华文明的起源与发展</div>
                            </div>
                            <div class="knowledge-point" data-id="2">
                                <div class="knowledge-point-title">史前文化遗址</div>
                                <div class="knowledge-point-desc">了解重要的史前文化遗址</div>
                            </div>
                            <div class="knowledge-point" data-id="3">
                                <div class="knowledge-point-title">原始农业</div>
                                <div class="knowledge-point-desc">原始农业的发展与影响</div>
                            </div>
                            <div class="knowledge-point" data-id="4">
                                <div class="knowledge-point-title">原始手工业</div>
                                <div class="knowledge-point-desc">原始手工业的发展与技术</div>
                            </div>
                            <div class="knowledge-point" data-id="5">
                                <div class="knowledge-point-title">氏族公社</div>
                                <div class="knowledge-point-desc">氏族公社的组织与变革</div>
                            </div>
                            <div class="knowledge-point" data-id="6">
                                <div class="knowledge-point-title">夏朝的建立</div>
                                <div class="knowledge-point-desc">夏朝的建立与历史地位</div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">已选知识点</label>
                        <div class="tags" id="selectedKnowledgeTags">
                            <!-- 动态生成的标签 -->
                        </div>
                    </div>
                </div>
                
                <!-- 步骤2：题型配置 -->
                <div class="step-content" id="step2Content" style="display: none;">
                    <div class="form-group">
                        <label class="form-label">试卷设置</label>
                        <div class="grid grid-3">
                            <div>
                                <label class="form-label">试卷标题</label>
                                <input type="text" class="form-input" id="quizTitle" placeholder="例如：明朝的建立 - 单元测试" value="明朝的建立 - 单元测试">
                            </div>
                            <div>
                                <label class="form-label">总分值</label>
                                <input type="number" class="form-input" id="totalScore" value="100">
                            </div>
                            <div>
                                <label class="form-label">难度系数</label>
                                <select class="form-select" id="difficultyLevel">
                                    <option value="easy">简单</option>
                                    <option value="medium" selected>中等</option>
                                    <option value="hard">困难</option>
                                </select>
                            </div>
                        </div>
                        
                        <div class="grid grid-3">
                            <div>
                                <label class="form-label">考试时长（分钟）</label>
                                <input type="number" class="form-input" id="examDuration" value="40">
                            </div>
                            <div>
                                <label class="form-label">适用年级</label>
                                <select class="form-select" id="targetGrade">
                                    <option value="7-1">七年级上册</option>
                                    <option value="7-2">七年级下册</option>
                                    <option value="8-1">八年级上册</option>
                                    <option value="8-2">八年级下册</option>
                                    <option value="9-1">九年级上册</option>
                                    <option value="9-2">九年级下册</option>
                                </select>
                            </div>
                            <div>
                                <label class="form-label">试卷类型</label>
                                <select class="form-select" id="quizType">
                                    <option value="unit">单元测试</option>
                                    <option value="midterm">期中考试</option>
                                    <option value="final">期末考试</option>
                                    <option value="practice">同步练习</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">题型配置</label>
                        
                        <!-- 选择题 -->
                        <div class="question-type">
                            <div class="question-type-header">
                                <div class="question-type-title">
                                    <span style="width: 30px; height: 30px; background-color: #eef2ff; color: #4361ee; border-radius: 50%; display: flex; align-items: center; justify-content: center; font-weight: bold;">A</span>
                                    选择题
                                </div>
                                <div class="question-type-controls">
                                    <label class="switch">
                                        <input type="checkbox" checked id="mcqEnabled">
                                        <span class="slider"></span>
                                    </label>
                                </div>
                            </div>
                            <div class="question-type-desc">
                                单选题，每题提供4个选项
                            </div>
                            <div class="tags">
                                <div class="tag">基础知识</div>
                                <div class="tag">历史事件</div>
                                <div class="tag">历史人物</div>
                                <div class="tag">
                                    <span>+ 添加标签</span>
                                </div>
                            </div>
                            <div class="question-type-controls">
                                <div class="counter">
                                    <button class="counter-btn" id="mcqDecrement">-</button>
                                    <input type="number" class="counter-input" id="mcqCount" value="10" min="0" max="20">
                                    <button class="counter-btn" id="mcqIncrement">+</button>
                                </div>
                                <div>每题</div>
                                <div class="counter">
                                    <button class="counter-btn" id="mcqScoreDecrement">-</button>
                                    <input type="number" class="counter-input" id="mcqScore" value="3" min="1" max="10">
                                    <button class="counter-btn" id="mcqScoreIncrement">+</button>
                                </div>
                                <div>分</div>
                            </div>
                        </div>
                        
                        <!-- 填空题 -->
                        <div class="question-type">
                            <div class="question-type-header">
                                <div class="question-type-title">
                                    <span style="width: 30px; height: 30px; background-color: #f3e8fa; color: #7209b7; border-radius: 50%; display: flex; align-items: center; justify-content: center; font-weight: bold;">B</span>
                                    填空题
                                </div>
                                <div class="question-type-controls">
                                    <label class="switch">
                                        <input type="checkbox" checked id="fillEnabled">
                                        <span class="slider"></span>
                                    </label>
                                </div>
                            </div>
                            <div class="question-type-desc">
                                考察关键概念、年代、人物等
                            </div>
                            <div class="tags">
                                <div class="tag">重要年代</div>
                                <div class="tag">历史事件</div>
                                <div class="tag">
                                    <span>+ 添加标签</span>
                                </div>
                            </div>
                            <div class="question-type-controls">
                                <div class="counter">
                                    <button class="counter-btn" id="fillDecrement">-</button>
                                    <input type="number" class="counter-input" id="fillCount" value="5" min="0" max="20">
                                    <button class="counter-btn" id="fillIncrement">+</button>
                                </div>
                                <div>每题</div>
                                <div class="counter">
                                    <button class="counter-btn" id="fillScoreDecrement">-</button>
                                    <input type="number" class="counter-input" id="fillScore" value="2" min="1" max="10">
                                    <button class="counter-btn" id="fillScoreIncrement">+</button>
                                </div>
                                <div>分</div>
                            </div>
                        </div>
                        
                        <!-- 简答题 -->
                        <div class="question-type">
                            <div class="question-type-header">
                                <div class="question-type-title">
                                    <span style="width: 30px; height: 30px; background-color: #e3f8ff; color: #4cc9f0; border-radius: 50%; display: flex; align-items: center; justify-content: center; font-weight: bold;">C</span>
                                    简答题
                                </div>
                                <div class="question-type-controls">
                                    <label class="switch">
                                        <input type="checkbox" checked id="shortEnabled">
                                        <span class="slider"></span>
                                    </label>
                                </div>
                            </div>
                            <div class="question-type-desc">
                                考察学生对历史知识的理解和表达能力
                            </div>
                            <div class="tags">
                                <div class="tag">历史评价</div>
                                <div class="tag">历史意义</div>
                                <div class="tag">历史影响</div>
                                <div class="tag">
                                    <span>+ 添加标签</span>
                                </div>
                            </div>
                            <div class="question-type-controls">
                                <div class="counter">
                                    <button class="counter-btn" id="shortDecrement">-</button>
                                    <input type="number" class="counter-input" id="shortCount" value="3" min="0" max="10">
                                    <button class="counter-btn" id="shortIncrement">+</button>
                                </div>
                                <div>每题</div>
                                <div class="counter">
                                    <button class="counter-btn" id="shortScoreDecrement">-</button>
                                    <input type="number" class="counter-input" id="shortScore" value="5" min="1" max="15">
                                    <button class="counter-btn" id="shortScoreIncrement">+</button>
                                </div>
                                <div>分</div>
                            </div>
                        </div>
                        
                        <!-- 材料分析题 -->
                        <div class="question-type">
                            <div class="question-type-header">
                                <div class="question-type-title">
                                    <span style="width: 30px; height: 30px; background-color: #e8f8f0; color: #2ecc71; border-radius: 50%; display: flex; align-items: center; justify-content: center; font-weight: bold;">D</span>
                                    材料分析题
                                </div>
                                <div class="question-type-controls">
                                    <label class="switch">
                                        <input type="checkbox" checked id="materialEnabled">
                                        <span class="slider"></span>
                                    </label>
                                </div>
                            </div>
                            <div class="question-type-desc">
                                根据历史材料，考察学生的分析能力和历史思维
                            </div>
                            <div class="tags">
                                <div class="tag">史料解读</div>
                                <div class="tag">历史评价</div>
                                <div class="tag">
                                    <span>+ 添加标签</span>
                                </div>
                            </div>
                            <div class="question-type-controls">
                                <div class="counter">
                                    <button class="counter-btn" id="materialDecrement">-</button>
                                    <input type="number" class="counter-input" id="materialCount" value="2" min="0" max="5">
                                    <button class="counter-btn" id="materialIncrement">+</button>
                                </div>
                                <div>每题</div>
                                <div class="counter">
                                    <button class="counter-btn" id="materialScoreDecrement">-</button>
                                    <input type="number" class="counter-input" id="materialScore" value="10" min="5" max="20">
                                    <button class="counter-btn" id="materialScoreIncrement">+</button>
                                </div>
                                <div>分</div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">难度分布</label>
                        <div class="difficulty-distribution">
                            <div class="difficulty-slider">
                                <div class="difficulty-label">简单</div>
                                <div class="difficulty-bar">
                                    <div class="difficulty-fill difficulty-easy" style="width: 30%"></div>
                                </div>
                                <div class="difficulty-value">30%</div>
                            </div>
                            <div class="difficulty-slider">
                                <div class="difficulty-label">中等</div>
                                <div class="difficulty-bar">
                                    <div class="difficulty-fill difficulty-medium" style="width: 50%"></div>
                                </div>
                                <div class="difficulty-value">50%</div>
                            </div>
                            <div class="difficulty-slider">
                                <div class="difficulty-label">困难</div>
                                <div class="difficulty-bar">
                                    <div class="difficulty-fill difficulty-hard" style="width: 20%"></div>
                                </div>
                                <div class="difficulty-value">20%</div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <div class="batch-actions" style="justify-content: flex-end;">
                            <button class="btn btn-secondary" id="resetConfig">重置</button>
                            <button class="btn btn-primary" id="smartRecommend">智能推荐</button>
                        </div>
                    </div>
                </div>
                
                <!-- 步骤3：预览与导出 -->
                <div class="step-content" id="step3Content" style="display: none;">
                    <div class="batch-toolbar">
                        <div class="batch-info">
                            <div>
                                <strong>试卷标题：</strong>
                                <span id="previewTitle">明朝的建立 - 单元测试</span>
                            </div>
                            <div>
                                <strong>总分：</strong>
                                <span id="previewScore">100</span>分
                            </div>
                            <div>
                                <strong>时长：</strong>
                                <span id="previewDuration">40</span>分钟
                            </div>
                        </div>
                        <div class="batch-actions">
                            <button class="btn btn-secondary btn-sm" id="regenerateBtn">
                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                    <path d="M21.5 2v6h-6M2.5 22v-6h6M2 11.5a10 10 0 0 1 18.8-4.3M22 12.5a10 10 0 0 1-18.8 4.2"/>
                                </svg>
                                重新生成
                            </button>
                            <button class="btn btn-primary btn-sm" id="exportBtn">
                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                    <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"/>
                                    <polyline points="7 10 12 15 17 10"/>
                                    <line x1="12" y1="15" x2="12" y2="3"/>
                                </svg>
                                导出试卷
                            </button>
                        </div>
                    </div>
                    
                    <div class="preview-container">
                        <div class="preview-header">
                            <div class="preview-title">明朝的建立 - 单元测试</div>
                            <div class="preview-meta">
                                <div>总分：100分</div>
                                <div>时间：40分钟</div>
                                <div>适用：七年级下册</div>
                            </div>
                        </div>
                        
                        <!-- 选择题部分 -->
                        <div class="preview-section">
                            <div class="preview-section-title">一、选择题（每题3分，共30分）</div>
                            <div class="question-list">
                                <div class="question-item">
                                    <div class="question-content">朱元璋建立的朝代是（ ）</div>
                                    <ul class="options-list">
                                        <li class="option-item">
                                            <div class="option-label">A.</div>
                                            <div>元朝</div>
                                        </li>
                                        <li class="option-item">
                                            <div class="option-label">B.</div>
                                            <div>明朝</div>
                                        </li>
                                        <li class="option-item">
                                            <div class="option-label">C.</div>
                                            <div>清朝</div>
                                        </li>
                                        <li class="option-item">
                                            <div class="option-label">D.</div>
                                            <div>宋朝</div>
                                        </li>
                                    </ul>
                                </div>
                                
                                <div class="question-item">
                                    <div class="question-content">明朝的都城最初定在（ ）</div>
                                    <ul class="options-list">
                                        <li class="option-item">
                                            <div class="option-label">A.</div>
                                            <div>北京</div>
                                        </li>
                                        <li class="option-item">
                                            <div class="option-label">B.</div>
                                            <div>南京</div>
                                        </li>
                                        <li class="option-item">
                                            <div class="option-label">C.</div>
                                            <div>开封</div>
                                        </li>
                                        <li class="option-item">
                                            <div class="option-label">D.</div>
                                            <div>洛阳</div>
                                        </li>
                                    </ul>
                                </div>
                                
                                <div class="question-item">
                                    <div class="question-content">朱元璋起义时，元朝统治已经出现严重危机，其中不包括（ ）</div>
                                    <ul class="options-list">
                                        <li class="option-item">
                                            <div class="option-label">A.</div>
                                            <div>民族矛盾激化</div>
                                        </li>
                                        <li class="option-item">
                                            <div class="option-label">B.</div>
                                            <div>自然灾害频发</div>
                                        </li>
                                        <li class="option-item">
                                            <div class="option-label">C.</div>
                                            <div>农民起义爆发</div>
                                        </li>
                                        <li class="option-item">
                                            <div class="option-label">D.</div>
                                            <div>外国入侵</div>
                                        </li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 填空题部分 -->
                        <div class="preview-section">
                            <div class="preview-section-title">二、填空题（每题2分，共10分）</div>
                            <div class="question-list">
                                <div class="question-item">
                                    <div class="question-content">朱元璋建立明朝的年号是________。</div>
                                </div>
                                
                                <div class="question-item">
                                    <div class="question-content">明朝的第二位皇帝是________，他曾发动"靖难之役"。</div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 简答题部分 -->
                        <div class="preview-section">
                            <div class="preview-section-title">三、简答题（每题5分，共15分）</div>
                            <div class="question-list">
                                <div class="question-item">
                                    <div class="question-content">简述朱元璋建立明朝的过程。</div>
                                </div>
                                
                                <div class="question-item">
                                    <div class="question-content">分析明朝初期的政治制度有哪些特点。</div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 材料分析题部分 -->
                        <div class="preview-section">
                            <div class="preview-section-title">四、材料分析题（每题10分，共20分）</div>
                            <div class="question-list">
                                <div class="question-item">
                                    <div class="question-content">
                                        <p>材料一：朱元璋说："高筑墙，广积粮，缓称王。"</p>
                                        <p>材料二：朱元璋说："各家自收田粮，归仓归库，以备军国之用。"</p>
                                        <p>请结合所学知识，分析朱元璋的这些政策对明朝建立和巩固的意义。</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 答案部分 -->
                        <div class="answer-section">
                            <div class="preview-section-title">参考答案</div>
                            <div class="question-list">
                                <div class="question-item">
                                    <div class="question-content">一、选择题</div>
                                    <div>1. B &nbsp;&nbsp; 2. B &nbsp;&nbsp; 3. D</div>
                                </div>
                                
                                <div class="question-item">
                                    <div class="question-content">二、填空题</div>
                                    <div>1. 洪武 &nbsp;&nbsp; 2. 朱棣</div>
                                </div>
                                
                                <div class="question-item">
                                    <div class="question-content">三、简答题</div>
                                    <div>1. 朱元璋建立明朝的过程：</div>
                                    <p>朱元璋出身贫苦农民，元末加入红巾军。1356年攻占集庆（今南京），后改名应天。1368年，朱元璋称帝，建立明朝，定都南京，年号洪武。此后，他派兵北伐，推翻元朝统治，统一全国。</p>
                                    
                                    <div>2. 明朝初期政治制度特点：</div>
                                    <p>废除丞相，权力高度集中；设立内阁，辅助皇帝处理政务；加强对地方的控制，实行三司分权；建立特务机构锦衣卫、东厂等；实行"海禁"政策等。</p>
                                </div>
                                
                                <div class="question-item">
                                    <div class="question-content">四、材料分析题</div>
                                    <p>朱元璋的这些政策对明朝建立和巩固的意义：</p>
                                    <p>1. "高筑墙，广积粮，缓称王"体现了朱元璋的战略思想，即先积蓄力量，稳固根基，再谋求大业，这有利于在复杂的政治军事环境中站稳脚跟。</p>
                                    <p>2. "各家自收田粮"的政策表明朱元璋重视农业生产和粮食储备，为军事行动和国家稳定提供了物质保障。</p>
                                    <p>3. 这些政策体现了朱元璋的务实精神和长远眼光，为明朝的建立和早期巩固奠定了基础。</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">导出选项</label>
                        <div class="export-options">
                            <div class="export-option selected" data-format="word">
                                <div class="export-icon">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                        <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
                                        <polyline points="14 2 14 8 20 8"></polyline>
                                        <path d="M9 13h6"></path>
                                        <path d="M9 17h6"></path>
                                        <path d="M9 9h1"></path>
                                    </svg>
                                </div>
                                <div class="export-title">Word文档</div>
                                <div class="export-desc">导出为可编辑的Word文档</div>
                            </div>
                            
                            <div class="export-option" data-format="pdf">
                                <div class="export-icon">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                        <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
                                        <polyline points="14 2 14 8 20 8"></polyline>
                                        <path d="M9 15h6"></path>
                                        <path d="M9 11h6"></path>
                                    </svg>
                                </div>
                                <div class="export-title">PDF文档</div>
                                <div class="export-desc">导出为不可编辑的PDF文档</div>
                            </div>
                            
                            <div class="export-option" data-format="html">
                                <div class="export-icon">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                        <polyline points="16 18 22 12 16 6"></polyline>
                                        <polyline points="8 6 2 12 8 18"></polyline>
                                    </svg>
                                </div>
                                <div class="export-title">HTML网页</div>
                                <div class="export-desc">导出为可在浏览器中打开的网页</div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">导出内容</label>
                        <div class="tags">
                            <div class="tag">
                                <input type="checkbox" id="exportQuestions" checked>
                                <label for="exportQuestions">试题</label>
                            </div>
                            <div class="tag">
                                <input type="checkbox" id="exportAnswers" checked>
                                <label for="exportAnswers">答案</label>
                            </div>
                            <div class="tag">
                                <input type="checkbox" id="exportAnalysis">
                                <label for="exportAnalysis">解析</label>
                            </div>
                            <div class="tag">
                                <input type="checkbox" id="exportScoring">
                                <label for="exportScoring">评分标准</label>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="modal-footer">
        <button class="btn btn-secondary" id="prevBtn" style="display: none;">
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <line x1="19" y1="12" x2="5" y2="12"></line>
                <polyline points="12 19 5 12 12 5"></polyline>
            </svg>
            上一步
        </button>
        <button class="btn btn-primary" id="nextBtn">
            下一步
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <line x1="5" y1="12" x2="19" y2="12"></line>
                <polyline points="12 5 19 12 12 19"></polyline>
            </svg>
        </button>
        <button class="btn btn-primary" id="generateBtn" style="display: none;">
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="M12 2v4M12 18v4M4.93 4.93l2.83 2.83M16.24 16.24l2.83 2.83M2 12h4M18 12h4M4.93 19.07l2.83-2.83M16.24 7.76l2.83-2.83"/>
            </svg>
            生成试卷
        </button>
    </div>
</body>
</html>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 获取DOM元素
            const steps = document.querySelectorAll('.step');
            const stepsProgress = document.querySelector('.steps-progress');
            const stepContents = document.querySelectorAll('.step-content');
            const prevBtn = document.getElementById('prevBtn');
            const nextBtn = document.getElementById('nextBtn');
            const generateBtn = document.getElementById('generateBtn');
            const closeBtn = document.querySelector('.modal-close');
            
            // 知识点选择
            const knowledgePoints = document.querySelectorAll('.knowledge-point');
            const selectedKnowledgeTags = document.getElementById('selectedKnowledgeTags');
            
            // 计数器按钮
            const counterBtns = document.querySelectorAll('.counter-btn');
            
            // 导出选项
            const exportOptions = document.querySelectorAll('.export-option');
            
            // 当前步骤
            let currentStep = 1;
            
            // 更新步骤显示
            function updateSteps(step) {
                // 更新步骤指示器
                steps.forEach(s => {
                    const stepNum = parseInt(s.dataset.step);
                    s.classList.remove('active', 'completed');
                    
                    if (stepNum === step) {
                        s.classList.add('active');
                    } else if (stepNum < step) {
                        s.classList.add('completed');
                    }
                });
                
                // 更新进度条
                const progressWidth = ((step - 1) / (steps.length - 1)) * 100;
                stepsProgress.style.width = `${progressWidth}%`;
                
                // 更新内容显示
                stepContents.forEach((content, index) => {
                    content.style.display = index + 1 === step ? 'block' : 'none';
                });
                
                // 更新按钮显示
                prevBtn.style.display = step > 1 ? 'inline-flex' : 'none';
                nextBtn.style.display = step < steps.length ? 'inline-flex' : 'none';
                generateBtn.style.display = step === 2 ? 'inline-flex' : 'none';
                
                // 更新当前步骤
                currentStep = step;
            }
            
            // 下一步按钮点击事件
            nextBtn.addEventListener('click', function() {
                if (currentStep < steps.length) {
                    updateSteps(currentStep + 1);
                }
            });
            
            // 上一步按钮点击事件
            prevBtn.addEventListener('click', function() {
                if (currentStep > 1) {
                    updateSteps(currentStep - 1);
                }
            });
            
            // 关闭按钮点击事件
            closeBtn.addEventListener('click', function() {
                // 这里可以添加关闭模态框的逻辑
                alert('关闭模态框');
            });
            
            // 知识点选择事件
            knowledgePoints.forEach(point => {
                point.addEventListener('click', function() {
                    this.classList.toggle('selected');
                    updateSelectedKnowledgeTags();
                });
            });
            
            // 更新已选知识点标签
            function updateSelectedKnowledgeTags() {
                selectedKnowledgeTags.innerHTML = '';
                
                const selectedPoints = document.querySelectorAll('.knowledge-point.selected');
                
                if (selectedPoints.length === 0) {
                    const emptyTag = document.createElement('div');
                    emptyTag.className = 'tag empty-tag';
                    emptyTag.textContent = '暂无选择';
                    selectedKnowledgeTags.appendChild(emptyTag);
                } else {
                    selectedPoints.forEach(point => {
                        const title = point.querySelector('.knowledge-point-title').textContent;
                        const tag = document.createElement('div');
                        tag.className = 'tag';
                        
                        const tagContent = document.createElement('span');
                        tagContent.textContent = title;
                        
                        const removeBtn = document.createElement('button');
                        removeBtn.className = 'tag-remove';
                        removeBtn.innerHTML = '&times;';
                        removeBtn.addEventListener('click', function(e) {
                            e.stopPropagation();
                            point.classList.remove('selected');
                            updateSelectedKnowledgeTags();
                        });
                        
                        tag.appendChild(tagContent);
                        tag.appendChild(removeBtn);
                        selectedKnowledgeTags.appendChild(tag);
                    });
                }
            }
            
            // 计数器按钮点击事件
            counterBtns.forEach(btn => {
                btn.addEventListener('click', function() {
                    const input = this.parentElement.querySelector('.counter-input');
                    const currentValue = parseInt(input.value);
                    const min = parseInt(input.min);
                    const max = parseInt(input.max);
                    
                    if (this.id.includes('Increment')) {
                        if (currentValue < max) {
                            input.value = currentValue + 1;
                        }
                    } else if (this.id.includes('Decrement')) {
                        if (currentValue > min) {
                            input.value = currentValue - 1;
                        }
                    }
                });
            });
            
            // 导出选项点击事件
            exportOptions.forEach(option => {
                option.addEventListener('click', function() {
                    exportOptions.forEach(opt => opt.classList.remove('selected'));
                    this.classList.add('selected');
                });
            });
            
            // 生成试卷按钮点击事件
            generateBtn.addEventListener('click', function() {
                // 这里可以添加生成试卷的逻辑
                updateSteps(3); // 直接跳转到第三步
            });
            
            // 重新生成按钮点击事件
            document.getElementById('regenerateBtn').addEventListener('click', function() {
                // 这里可以添加重新生成试卷的逻辑
                alert('重新生成试卷');
            });
            
            // 导出试卷按钮点击事件
            document.getElementById('exportBtn').addEventListener('click', function() {
                // 获取选中的导出格式
                const selectedFormat = document.querySelector('.export-option.selected').dataset.format;
                // 获取导出内容选项
                const exportQuestions = document.getElementById('exportQuestions').checked;
                const exportAnswers = document.getElementById('exportAnswers').checked;
                const exportAnalysis = document.getElementById('exportAnalysis').checked;
                const exportScoring = document.getElementById('exportScoring').checked;
                
                // 这里可以添加导出试卷的逻辑
                alert(`导出格式：${selectedFormat}，导出内容：题目(${exportQuestions})、答案(${exportAnswers})、解析(${exportAnalysis})、评分标准(${exportScoring})`);
            });
            
            // 智能推荐按钮点击事件
            document.getElementById('smartRecommend').addEventListener('click', function() {
                // 这里可以添加智能推荐的逻辑
                alert('智能推荐配置');
            });
            
            // 重置配置按钮点击事件
            document.getElementById('resetConfig').addEventListener('click', function() {
                // 这里可以添加重置配置的逻辑
                alert('重置配置');
            });
            
            // 初始化已选知识点标签
            updateSelectedKnowledgeTags();
        });
    </script>
</body>
</html>
